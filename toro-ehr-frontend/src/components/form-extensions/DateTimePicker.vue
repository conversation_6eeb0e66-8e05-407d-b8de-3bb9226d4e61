<template>
  <VueDatePicker
    v-model="internalValue"
    :teleport="true"
  >
    <template #dp-input="{ value }">
      <InputText
        :id="id"
        :label="label"
        :wrapper-class="wrapperClass"
        :modelValue="value"
      />
    </template>
  </VueDatePicker>
</template>

<script setup>
import { computed } from 'vue';
import InputText from './InputText.vue';

defineProps({
  modelValue: {
    type: [Date, String], // Supports Date or String
    required: true,
  },
  id: {
    type: String,
    required: false,
    default: 'datepicker-input',
  },
  label: {
    type: String,
    required: false,
    default: 'Date',
  },
  wrapperClass: {
    type: String,
    required: false,
    default: 'mb-2 mt-1',
  },
});

defineEmits(['update:modelValue']);

// Use a computed property to manage v-model binding
const internalValue = computed({
  get: () => modelValue,
  set: (newValue) => {
    emit('update:modelValue', newValue);
  },
});
</script>

<style>
/* Add any specific styles if needed */
</style>
