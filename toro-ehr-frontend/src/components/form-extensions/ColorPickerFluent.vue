<template>
  <div :class="['relative', wrapperClass]">
    <div class="my-3">
      <div class="flex items-center space-x-2">
        <label> Calendar Color </label>
        <ColorPicker :id="id" v-model="value" :invalid="!!errorMessage" v-bind="attrs" />
      </div>
    </div>
    <transition name="fade">
      <small v-if="errorMessage" class="error-message">
        {{ errorMessage }}
      </small>
    </transition>
  </div>
</template>

<script setup lang="ts">
import { useField } from 'vee-validate'
import { useAttrs, type SetupContext } from 'vue'
import ColorPicker from 'primevue/colorpicker'

const attrs: SetupContext['attrs'] = useAttrs()
defineEmits(['update:modelValue', 'focus'])

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  label: {
    type: String,
    required: true,
  },
  wrapperClass: {
    type: String,
    default: '',
  },
  fluid: {
    type: Boolean,
    default: true,
  },
  modelValue: String,
})

const { value, errorMessage } = useField<string | null>(() => props.id, undefined, {
  syncVModel: true,
  validateOnValueUpdate: false,
})
</script>

<style scoped>
/* Fade transition styles */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.error-message {
  position: absolute;
  bottom: -0.5rem;
  right: 0.5rem;
  transform: scale(0.75);
  color: #dc2626; /* Tailwind's red-600 */
  --tw-bg-opacity: 1;
  background-color: white;
  padding-left: 0.625rem;
  text-align: right;
  padding-right: 0.625rem;
}
</style>
