<template>
  <div :class="['relative', wrapperClass]">
    <div class="relative">
      <input
        :id="id"
        type="file"
        @change="handleFileChange"
        :accept="accept"
        :class="[
          'file:mr-4 file:rounded-full file:border-0  file:px-4 file:py-2 file:text-sm file:font-semibold file:text-white',
          errorMessage
            ? 'file:bg-red-600 hover:file:bg-red-600'
            : 'file:bg-toroblue-500 hover:file:bg-toroblue-600',
          meta.valid ? 'valid' : '',
        ]"
      />
    </div>
    <transition name="fade">
      <small v-if="errorMessage" class="error-message">
        {{ errorMessage }}
      </small>
    </transition>
  </div>
</template>

<script setup lang="ts">
import { useField } from 'vee-validate'

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  label: {
    type: String,
    required: true,
  },
  placeholder: {
    type: String,
    default: '',
  },
  wrapperClass: {
    type: String,
    default: 'mb-8',
  },
  accept: {
    type: String,
    default: '*',
  },
  type: String,
  modelValue: File || null,
})

const handleFileChange = async (event: Event) => {
  const input = event.target as HTMLInputElement
  const file = input.files?.[0] || null
  modelValue.value = file || null

  await validateFile()
}

const {
  value: modelValue,
  errorMessage,
  meta,
  validate: validateFile,
} = useField(() => props.id, undefined, {
  syncVModel: true,
  validateOnValueUpdate: false,
})
</script>

<style scoped>
/* Fade transition styles */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.error-message {
  position: absolute;
  bottom: -0.5rem;
  right: 0.5rem;
  transform: scale(0.75);
  color: #dc2626; /* Tailwind's red-600 */
  --tw-bg-opacity: 1;
  background-color: white;
  padding-left: 0.625rem;
  text-align: right;
  padding-right: 0.625rem;
}
</style>
