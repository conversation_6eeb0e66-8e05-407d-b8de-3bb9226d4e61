<template>
  <div class="flex flex-col min-h-screen">
    <AppNavigation v-if="isNavbarVisible()" />
    <div class="flex-grow">
      <RouterView />
    </div>
    <AppFooter />
  </div>
</template>

<script setup lang="ts">
import { useRoute } from 'vue-router'
import AppFooter from './components/layout/AppFooter.vue'
import AppNavigation from './components/layout/AppNavigation.vue'
import { computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { api } from './api'

// Get the current route
const route = useRoute()

//todo: group all routes where header should be hidden
// Check if the current page is the login page or set password
const isNavbarVisible = () => route.name !== 'login' && route.name !== 'set-password-employee'
/*const isNavbarVisible = computed(
  () => route.name !== 'login' && route.name !== 'set-password-employee',
)*/

const authStore = useAuthStore()

onMounted(() => {
  const storedToken = localStorage.getItem('token')
  if (storedToken) {
    api.setSecurityData(storedToken)
  }

  const storedUser = JSON.parse(localStorage.getItem('user') || 'null')
  if (storedUser) {
    authStore.setUser(storedUser)
  }
})
</script>
