import './assets/tailwind.css'
import './assets/styles.css'

import 'preline/preline'
import '@vuepic/vue-datepicker/dist/main.css'
import VueDatePicker from '@vuepic/vue-datepicker'
import 'primeicons/primeicons.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import Toast, { type PluginOptions } from 'vue-toastification'
import 'vue-toastification/dist/index.css'
import PrimeVue from 'primevue/config'
import Tooltip from 'primevue/tooltip'
import ConfirmationService from 'primevue/confirmationservice'
// @ts-ignore
import VueDraggableResizable from 'vue-draggable-resizable'

import App from './App.vue'
import router from './router'
import { setRouterInstance } from '@/utils/routerUtils'

const app = createApp(App)

app.use(PrimeVue, {
  theme: 'none',
  options: {
    darkModeSelector: 'none',
  }
})
app.use(ConfirmationService)
app.directive('tooltip', Tooltip)

const options: PluginOptions = {
  // You can set your default options here
}

app.use(createPinia())
app.use(Toast, options)
app.use(router)
setRouterInstance(router)

app.component('VueDatePicker', VueDatePicker)
app.component('vue-draggable-resizable', VueDraggableResizable)

app.mount('#app')
