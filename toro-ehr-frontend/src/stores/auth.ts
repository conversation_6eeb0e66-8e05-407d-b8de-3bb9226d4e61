import { defineStore } from 'pinia'
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import type {
  LoginUserCommand,
  SetPasswordEmployeeCommand,
  UpdateSessionCommand,
  UserInfoResponse,
} from '../api/api-reference'
import { api } from '../api'

export const useAuthStore = defineStore('auth', () => {
  const user = ref<UserInfoResponse | null>(null)

  const router = useRouter()

  async function login(loginUserCommand: LoginUserCommand) {
    try {
      const loginResponse = await api.authentication.authenticationLoginUserPost(loginUserCommand)
      // if (!response.ok) throw new Error(data.message || 'Login failed')
      localStorage.setItem('token', loginResponse.data)
      api.setSecurityData(loginResponse.data)

      // Fetch user data
      await getUser()
    } catch (error) {
      console.error(error)
      //throw error // Pass the error to the component for handling
    }
  }

  async function setPassword(
    setPasswordPractitionerCommand: SetPasswordEmployeeCommand,
    isPatient: boolean,
  ) {
    try {
      let setPasswordResponse
      if (isPatient) {
        setPasswordResponse = await api.authentication.authenticationSetPasswordPatient(
          setPasswordPractitionerCommand,
        )
      } else {
        setPasswordResponse = await api.authentication.authenticationSetPasswordEmployee(
          setPasswordPractitionerCommand,
        )
      }

      localStorage.setItem('token', setPasswordResponse.data)
      api.setSecurityData(setPasswordResponse.data)

      // Fetch user data
      await getUser()
      if (isPatient) {
        await router.push('/my-appointments')
      } else {
        await router.push('/dashboard')
      }
    } catch (error) {
      console.error(error)
      // router.push('/organizations')
      //throw error // Pass the error to the component for handling
    }
  }

  async function getUser() {
    try {
      // Fetch user data
      const currentUserResponse = await api.authentication.authenticationLoginUserGet()
      setUser(currentUserResponse.data)
      localStorage.setItem('user', JSON.stringify(user.value))
    } catch (error) {
      console.error(error)
      // router.push('/organizations')
      //throw error // Pass the error to the component for handling
    }
  }

  function setUser(userInfo: UserInfoResponse) {
    user.value = userInfo
  }

  function logout() {
    removeUserAndLocalStorage()
    router.push('/login')
  }

  function removeUserAndLocalStorage() {
    user.value = null
    localStorage.removeItem('token')
    localStorage.removeItem('user')
  }

  async function updateSession(command: UpdateSessionCommand) {
    try {
      await api.authentication.authenticationUpdateSession(command)
      await getUser()
    } catch (error) {
      console.error(error)
    }
  }

  return {
    user,
    login,
    logout,
    getUser,
    setUser,
    setPassword,
    removeUserAndLocalStorage,
    updateSession,
  }
})
