import { defineStore } from 'pinia'
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import type {  PatientProfileResponse } from '../api/api-reference'
import { api } from '../api'

export const usePatientStore = defineStore('patient', () => {
  const patientProfile = ref<PatientProfileResponse | null>(null)

  const router = useRouter()

  async function getPatientProfile() {
    try {
      // Fetch profile
      const patientProfile = await api.patients.patientGetPatientProfile()
      setPatientProfile(patientProfile.data)
    } catch (error) {
      console.error(error)
    }
  }

  function setPatientProfile(patientProfileResponse: PatientProfileResponse) {
    patientProfile.value = patientProfileResponse
  }

  return { patientProfile, getPatientProfile }
})
