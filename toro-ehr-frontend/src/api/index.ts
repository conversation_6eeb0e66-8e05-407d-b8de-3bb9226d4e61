import { Api } from './api-reference'
import { useToast } from 'vue-toastification'
import { getRouterInstance } from '@/utils/routerUtils'

const toast = useToast()

export const api = new Api({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  securityWorker: (token) => (token ? { headers: { Authorization: `${String(token)}` } } : {}),
  format: 'json',
  secure: true,
})

api.instance.interceptors.response.use(
  (response) => response,
  async (error) => {
    const router = getRouterInstance()
    if (error.response) {
      const { status, data } = error.response

      switch (status) {
        case 401: // Unauthorized
          // todo: implement refresh token attempt
          await router.push({name: 'login'})
          break

        case 403: // Forbidden
          toast.error('You do not have permission to access this resource.')
          router.push({ name: 'forbidden' })
          break

        case 400: // Bad Request
          // Pass errors to the component via rejection
          return Promise.reject(data)

        case 404: // Not Found
          toast.error(data.title || 'The requested resource was not found.')
          break

        case 500: // Internal Server Error
          toast.error('An unexpected error occurred. Please try again later.')
          break

        default: // Handle other status codes
          toast.error('An unexpected error occurred.')
          break
      }
    } else {
      // Handle network errors
      toast.error('Network error. Please check your internet connection.')
    }

    return Promise.reject(error)
  },
)
