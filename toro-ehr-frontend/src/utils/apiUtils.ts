/**
 * Utility functions for handling API operations and preventing duplicate calls
 */

/**
 * Creates a debounced version of an async function that prevents multiple calls
 * while one is already in progress
 */
export function createSingleCallFunction<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  options: {
    // time to wait before allowing another call (in ms)
    debounceMs?: number
    // whether to prevent calls while one is in progress
    preventDuplicates?: boolean
  } = {}
): T {
  const { debounceMs = 0, preventDuplicates = true } = options

  let isExecuting = false
  let timeoutId: ReturnType<typeof setTimeout> | null = null

  return ((...args: any[]) => {
    return new Promise((resolve, reject) => {
      // clear existing timeout
      if (timeoutId) {
        clearTimeout(timeoutId)
      }

      // prevent duplicate calls if one is already executing
      if (preventDuplicates && isExecuting) {
        console.warn('Function call ignored - already executing')
        return resolve(null)
      }

      const executeFunction = async () => {
        if (preventDuplicates && isExecuting) {
          return resolve(null)
        }

        isExecuting = true

        try {
          const result = await fn(...args)
          resolve(result)
        } catch (error) {
          reject(error)
        } finally {
          isExecuting = false
        }
      }

      if (debounceMs > 0) {
        timeoutId = setTimeout(executeFunction, debounceMs)
      } else {
        executeFunction()
      }
    })
  }) as T
}

/**
 * Simple wrapper to prevent duplicate API calls for a function
 */
export function preventDuplicateCalls<T extends (...args: any[]) => Promise<any>>(fn: T): T {
  return createSingleCallFunction(fn, { preventDuplicates: true })
}

/**
 * Creates a debounced version of an API function
 */
export function debounceApiCall<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  delayMs: number = 300
): T {
  return createSingleCallFunction(fn, {
    debounceMs: delayMs,
    preventDuplicates: true
  })
}
