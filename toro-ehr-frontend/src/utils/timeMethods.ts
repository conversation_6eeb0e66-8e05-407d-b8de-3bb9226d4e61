import type { DayOfWeek } from '../api/api-reference'

export function formatDate(dateString: string | null | undefined): string {
  if (dateString) {
    const date = new Date(dateString)
    return date.toLocaleString('en-US', {
      month: 'numeric',
      day: 'numeric',
      year: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    })
  }
  return 'n/a'
}

export function formatDateWithoutTime(dateString: string | null | undefined): string {
  if (dateString) {
    const date = new Date(dateString)
    return date.toLocaleString('en-US', {
      month: 'numeric',
      day: 'numeric',
      year: 'numeric',
    })
  }
  return 'n/a'
}

export function formatTime(dateString: string | null | undefined): string {
  if (dateString) {
    const date = new Date(dateString)
    return date.toLocaleString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    })
  }
  return 'n/a'
}

export function getDayName(dayNumber: DayOfWeek | undefined) {
  const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
  if (dayNumber || dayNumber === 0) {
    return days[dayNumber]
  }
  return 'Unknown'
}

export function displayTimeSpan(timespan: string | null | undefined): string {
  if (timespan) {
    const date = new Date(`1970-01-01T${timespan}`) // Create a date object with the timespan
    return date.toLocaleString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true }) // Format as 12-hour with AM/PM
  }
  return 'n/a'
}
export function parseDateToTimeString(date: Date | null): string | null {
  if (!date) return null

  const hours = date.getHours().toString().padStart(2, '0')
  const minutes = date.getMinutes().toString().padStart(2, '0')

  return `${hours}:${minutes}`
}

export const parseTimeToToday = (time: string | null | undefined): Date | undefined => {
  if (!time) return undefined

  const [hours, minutes] = time.split(':').map(Number)
  const now = new Date()

  return new Date(now.getFullYear(), now.getMonth(), now.getDate(), hours, minutes, 0, 0)
}

export const convertToUtc = (date: Date): Date => {
  return new Date(
    Date.UTC(
      date.getFullYear(),
      date.getMonth(),
      date.getDate(),
      date.getHours(),
      date.getMinutes(),
      date.getSeconds(),
    ),
  )
}

export const formatDateTime = (dateTimeOffset: string) => {
  const date = new Date(dateTimeOffset)

  const formattedDate = date.toISOString().split('T')[0] // Extract YYYY-MM-DD
  const formattedTime = date.toLocaleTimeString([], {
    hour: '2-digit',
    minute: '2-digit',
    hour12: true,
  })

  return { formattedDate, formattedTime }
}
