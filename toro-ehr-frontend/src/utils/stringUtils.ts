export function pascalToSpaced(str: string) {
  return str.replace(/([A-Z])/g, ' $1').trim()
}

export function generateGuid(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0
    const v = c === 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}

export function getFileNameFromUrl(url: string | null): string {
  if (!url) return ''

  const parts = url.split('/')
  return parts[parts.length - 1]
}
