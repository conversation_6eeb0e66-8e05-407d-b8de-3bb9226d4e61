# Preventing Multiple API Calls - Best Practices

This document explains how to prevent multiple API calls when users click buttons repeatedly, using the utilities provided in this project.

## Problem

When users click save buttons or other action buttons multiple times quickly, it can result in:
- Multiple identical API calls being sent
- Race conditions
- Poor user experience
- Potential data corruption

## Solutions

We provide two main approaches to solve this problem:

### 1. Composable Approach (Recommended)

Use the `useAsyncOperation` or `useButtonOperation` composables for reactive loading states and automatic duplicate call prevention.

#### Basic Button Operation

```vue
<template>
  <Button 
    :label="isLoading ? 'Saving...' : 'Save'" 
    @click="handleSave"
    :loading="isLoading"
    :disabled="isLoading"
  />
</template>

<script setup lang="ts">
import { useButtonOperation } from '@/composables/useAsyncOperation'
import { useToast } from 'vue-toastification'

const toast = useToast()

const { execute: handleSave, isLoading } = useButtonOperation(
  async () => {
    // Your API call here
    await api.someEndpoint.save(data)
    toast.success("Saved successfully")
  },
  {
    onError: (error) => {
      toast.error("Failed to save")
    }
  }
)
</script>
```

#### Advanced Async Operation

```vue
<script setup lang="ts">
import { useAsyncOperation } from '@/composables/useAsyncOperation'

const { execute, isLoading, error, result } = useAsyncOperation(
  async (id: string) => {
    return await api.users.getUser(id)
  },
  {
    preventDuplicates: true,
    onSuccess: (user) => {
      console.log('User loaded:', user)
    },
    onError: (error) => {
      console.error('Failed to load user:', error)
    }
  }
)

// Usage
const loadUser = (userId: string) => {
  execute(userId)
}
</script>
```

### 2. Utility Function Approach

Use the utility functions for simpler cases or when you need more control.

#### Prevent Duplicate Calls

```typescript
import { preventDuplicateCalls } from '@/utils/apiUtils'

// Wrap your API function
const saveData = preventDuplicateCalls(async (data: any) => {
  return await api.save(data)
})

// Now multiple calls will be ignored while one is in progress
saveData(formData) // executes
saveData(formData) // ignored (logs warning)
saveData(formData) // ignored (logs warning)
```

#### Debounced API Calls

```typescript
import { debounceApiCall } from '@/utils/apiUtils'

// Create a debounced version that waits 500ms
const debouncedSearch = debounceApiCall(async (query: string) => {
  return await api.search(query)
}, 500)

// Usage in search input
const handleSearch = (query: string) => {
  debouncedSearch(query)
}
```

## Form Validation Integration

When using with form validation libraries like vee-validate:

```vue
<script setup lang="ts">
import { useForm } from 'vee-validate'
import { useButtonOperation } from '@/composables/useAsyncOperation'

const { handleSubmit } = useForm({
  // your validation schema
})

const { execute: executeSubmit, isLoading } = useButtonOperation(
  async (values: any) => {
    await api.submitForm(values)
    // handle success
  }
)

const onSubmit = handleSubmit(async (values) => {
  await executeSubmit(values)
})
</script>
```

## Global vs Component-Specific Solutions

### When to use Global (Composable) Approach:
- ✅ Forms with save buttons
- ✅ Action buttons that trigger API calls
- ✅ When you need loading states in the UI
- ✅ When you want consistent error handling

### When to use Utility Functions:
- ✅ Simple API wrapper functions
- ✅ Search functionality (with debouncing)
- ✅ Background operations without UI feedback
- ✅ When you need fine-grained control

## Migration Guide

To update existing components:

1. **Import the composable:**
   ```typescript
   import { useButtonOperation } from '@/composables/useAsyncOperation'
   ```

2. **Wrap your async function:**
   ```typescript
   const { execute: handleSave, isLoading } = useButtonOperation(yourAsyncFunction)
   ```

3. **Update your button:**
   ```vue
   <Button 
     :label="isLoading ? 'Saving...' : 'Save'"
     :loading="isLoading"
     :disabled="isLoading"
     @click="handleSave"
   />
   ```

## Examples in Codebase

- ✅ `PersonalInfo.vue` - Uses `useButtonOperation` for save functionality
- ✅ `ContactInfo.vue` - Uses `useButtonOperation` for save functionality
- ✅ `PaymentForm.vue` - Already has loading states for payment operations

## Best Practices

1. **Always show loading states** for better UX
2. **Disable buttons** while operations are in progress
3. **Provide clear feedback** on success/error
4. **Use debouncing** for search inputs (300-500ms)
5. **Prevent duplicates** for all save/submit operations
6. **Handle errors gracefully** with user-friendly messages

## Testing

When testing components that use these utilities:

```typescript
// Mock the composable in tests
vi.mock('@/composables/useAsyncOperation', () => ({
  useButtonOperation: () => ({
    execute: vi.fn(),
    isLoading: ref(false)
  })
}))
```
