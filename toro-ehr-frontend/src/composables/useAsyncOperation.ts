import { ref, type Ref } from 'vue'

/**
 * Composable for handling async operations with loading states and duplicate call prevention
 * 
 * @param operation - The async function to execute
 * @param options - Configuration options
 * @returns Object with execute function, loading state, and error state
 */
export function useAsyncOperation<T = any>(
  operation: (...args: any[]) => Promise<T>,
  options: {
    // prevent multiple calls while one is in progress
    preventDuplicates?: boolean
    // custom error handler
    onError?: (error: any) => void
    // custom success handler  
    onSuccess?: (result: T) => void
  } = {}
) {
  const isLoading = ref(false)
  const error = ref<any>(null)
  const result = ref<T | null>(null)

  const {
    preventDuplicates = true,
    onError,
    onSuccess
  } = options

  const execute = async (...args: any[]): Promise<T | null> => {
    // prevent duplicate calls if already loading
    if (preventDuplicates && isLoading.value) {
      console.warn('Operation already in progress, ignoring duplicate call')
      return null
    }

    isLoading.value = true
    error.value = null

    try {
      const operationResult = await operation(...args)
      result.value = operationResult
      
      if (onSuccess) {
        onSuccess(operationResult)
      }
      
      return operationResult
    } catch (err) {
      error.value = err
      
      if (onError) {
        onError(err)
      } else {
        console.error('Async operation failed:', err)
      }
      
      return null
    } finally {
      isLoading.value = false
    }
  }

  return {
    execute,
    isLoading: isLoading as Ref<boolean>,
    error: error as Ref<any>,
    result: result as Ref<T | null>
  }
}

/**
 * Simpler version for button operations that just need loading state and duplicate prevention
 */
export function useButtonOperation(
  operation: (...args: any[]) => Promise<any>,
  options: {
    onError?: (error: any) => void
    onSuccess?: (result: any) => void
  } = {}
) {
  const { execute, isLoading } = useAsyncOperation(operation, {
    preventDuplicates: true,
    ...options
  })

  return {
    execute,
    isLoading
  }
}
