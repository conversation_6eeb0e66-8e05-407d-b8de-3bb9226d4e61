<template>
  <div class="p-4 sm:p-5">
    <Card>
      <template #title> {{ isEditMode ? "Edit Note Template" : "Create Note Template" }}</template>
      <template #content>
        <div class="p-fluid">
          <!-- Title & Location in one row -->
          <div class="flex flex-col sm:flex-row gap-4 mb-4">
            <div class="w-full sm:w-1/3">
              <InputText id="name" label="Name" class="w-full"/>
            </div>
            <div class="w-full sm:w-1/3">
              <Select
                id="classification"
                :options="classifications"
                v-model="selectedClassification"
                optionLabel="label"
                label="Classification"
                optionValue="value"
                class="w-full"
              />
            </div>
            <div class="w-full sm:w-1/3">
              <Select
                id="specialization"
                :options="specializations"
                label="Specialization"
                optionLabel="label"
                optionValue="value"
                class="w-full"
              />
            </div>
          </div>

          <div class="flex flex-col sm:flex-row gap-4 mb-6">
            <MultiSelect
              v-if="isOrganizationAdmin && locations.length > 0"
              id="locations"
              label="Locations"
              :v-model="selectedLocations"
              :options="locations"
              optionLabel="text"
              optionValue="value"
              filter
              class="w-full"
            />

            <Select
              id="documentType"
              v-model="selectedDocumentType"
              :options="documentTypes.map(d => ({ label: d, value: d }))"
              label="Document Type"
              optionLabel="label"
              optionValue="value"
              class="w-full"
            />
          </div>

          <div v-if="selectedDocumentType" class="space-y-4">
            <div>
              <h4 class="font-semibold">Required Sections</h4>
              <div v-for="(section, index) in requiredSections" :key="index" class="mb-4">
                <!-- Case 1: Simple section -->
                <div v-if="typeof section === 'string'">
                  <label class="font-medium block mb-1">{{ section }}</label>
                  <Textarea v-model="sectionTexts[section]" autoResize class="w-full"/>
                </div>

                <!-- Case 2: Array of alternatives -->
                <div v-else-if="Array.isArray(section) && !Array.isArray(section[1])">
                  <label class="font-medium block mb-1">Select one: {{ section.join(' / ') }}</label>
                  <div class="flex flex-col gap-2">
                    <div v-for="option in section" :key="option" class="flex flex-col gap-1">
                      <div class="flex items-center gap-2">
                        <RadioButton
                          :name="'required-alt-' + index"
                          :value="option"
                          v-model="selectedRequiredAlternatives[index]"
                          :inputId="'alt-' + index + '-' + option"
                        />
                        <label :for="'alt-' + index + '-' + option">{{ option }}</label>
                      </div>
                      <Textarea
                        v-if="selectedRequiredAlternatives[index] === option"
                        v-model="sectionTexts[option]"
                        autoResize
                        class="w-full"
                      />
                    </div>
                  </div>
                </div>

                <!-- Case 3: One or both required group (custom handling for nested alternatives) -->
                <div v-else-if="Array.isArray(section[1])">
                  <label class="font-medium block mb-1">
                    Select one:
                    {{ section[0] }} or both {{ section[1].join(" + ") }}
                  </label>
                  <div class="flex flex-col gap-2">
                    <div class="flex items-center gap-2">
                      <RadioButton
                        :name="'required-combo-' + index"
                        :value="section[0]"
                        v-model="selectedRequiredAlternatives[index]"
                        :inputId="'combo-' + index + '-0'"
                      />
                      <label :for="'combo-' + index + '-0'">{{ section[0] }}</label>
                    </div>
                    <div class="flex flex-col gap-2">
                      <div class="flex items-center gap-2">
                        <RadioButton
                          :name="'required-combo-' + index"
                          value="both"
                          v-model="selectedRequiredAlternatives[index]"
                          :inputId="'combo-' + index + '-both'"
                        />
                        <label :for="'combo-' + index + '-both'">Both {{ section[1].join(" + ") }}</label>
                      </div>
                      <div v-if="selectedRequiredAlternatives[index] === 'both'" class="flex flex-col gap-2">
                        <div v-for="option in section[1]" :key="option">
                          <label class="font-medium block">{{ option }}</label>
                          <Textarea v-model="sectionTexts[option]" autoResize class="w-full"/>
                        </div>
                      </div>
                    </div>
                    <div v-if="selectedRequiredAlternatives[index] === section[0]">
                      <label class="font-medium block">{{ section[0] }}</label>
                      <Textarea v-model="sectionTexts[section[0]]" autoResize class="w-full"/>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div>
              <h4 class="font-semibold">Recommended Sections</h4>
              <div v-for="section in recommendedSections" :key="section" class="mb-2">
                <div class="flex items-center gap-2 mb-1">
                  <Checkbox v-model="selectedOptionalSections" :value="section" :inputId="section + '-rec'"/>
                  <label :for="section + '-rec'">{{ section }}</label>
                </div>
                <Textarea v-if="selectedOptionalSections.includes(section)" v-model="sectionTexts[section]" autoResize
                          class="w-full"/>
              </div>
            </div>

            <div>
              <h4 class="font-semibold">Additional Sections</h4>
              <div v-for="section in additionalSections" :key="section" class="mb-2">
                <div class="flex items-center gap-2 mb-1">
                  <Checkbox v-model="selectedOptionalSections" :value="section" :inputId="section + '-add'"/>
                  <label :for="section + '-add'">{{ section }}</label>
                </div>
                <Textarea v-if="selectedOptionalSections.includes(section)" v-model="sectionTexts[section]" autoResize
                          class="w-full"/>
              </div>
            </div>
          </div>

          <div v-if="selectedDocumentType">
            <h4 class="font-semibold">Custom Sections</h4>
            <div
              v-for="(section, index) in customSections"
              :key="index"
              class="border p-3 rounded mb-2 flex flex-col sm:block relative"
            >
              <div class="flex flex-col sm:flex-row gap-4 mb-2">
                <div class="w-full sm:w-1/3">
                  <InputText
                    :id="'customSections[' + index + '].name'"
                    label="Field name"
                    v-model="section.name"
                    class="w-full"
                  />
                </div>
                <div class="flex items-center gap-2">
                  <Checkbox
                    v-model="section.required"
                    :inputId="'customSections[' + index + '].required'"
                    binary
                  />
                  <label :for="'customSections[' + index + '].required'">Required</label>
                </div>
              </div>

              <Textarea
                :id="'customSections[' + index + '].content'"
                v-model="section.content"
                autoResize
                class="w-full"
                rows="3"
                placeholder="Section Content"
              />

              <i
                class="pi pi-trash text-red-500 cursor-pointer text-lg p-1 hover:bg-gray-100 rounded-full transition absolute right-2 sm:top-2 bottom-2 sm:bottom-auto"
                @click="customSections.splice(index, 1)"
              ></i>
            </div>
            <div class="flex justify-end mt-2">
              <Button icon="pi pi-plus-circle" label="Add field" variant="text"
                      class="text-blue-600 flex items-center gap-1 font-medium"
                      @click="customSections.push({ name: '', content: '', required: false })"/>
            </div>
          </div>


          <div class="card flex flex-col sm:flex-row justify-end gap-2 sm:gap-4 mb-2 mt-6">
            <Button class="px-4 py-2" label="Close" severity="secondary" variant="text"
                    @click="goToNoteTemplatesList()"/>
            <Button label="Save" @click="submitForm" class=""/>
          </div>
        </div>
      </template>
    </Card>
  </div>
</template>

<script setup lang="ts">
import {specialtiesCodes, documentSectionsMap } from "@/utils/specialties";
import InputText from "@/components/form-extensions/InputTextFluent.vue";
import Select from "@/components/form-extensions/SelectFluent.vue";
import {ref, watch, watchEffect, computed, onBeforeMount} from "vue";
import type {
  CreateNoteTemplateCommand,
  NoteTemplateFieldRequest,
  SelectListItem
} from "@/api/api-reference.ts";
import Button from "primevue/button";
import {api} from '@/api'
import Card from 'primevue/card';
import {useToast} from 'vue-toastification'
import {useField, useForm} from "vee-validate";
import * as yup from "yup";
import {useRoute, useRouter} from "vue-router";
import Textarea from 'primevue/textarea';
import Checkbox from 'primevue/checkbox';
import RadioButton from 'primevue/radiobutton';
import {useAuthStore} from "@/stores/auth.ts";
import MultiSelect from '@/components/form-extensions/MultiSelectFluent.vue';

const toast = useToast()
const route = useRoute();
const router = useRouter();

const authStore = useAuthStore()
const isOrganizationAdmin = ref(authStore.user?.selectedUserRole == 'Employee' && authStore.user?.locationEmployeeRoles?.includes("OrganziationAdmin"))
const isEditMode = ref(false);

onBeforeMount(async () => {
  if (isOrganizationAdmin.value) {
    await fetchLocations();
  }
})

const {handleSubmit, setFieldError, setValues} = useForm({
  validationSchema: yup.object({
    name: yup.string().required(),
    classification: yup.string().required(),
    documentType: yup.string().required(),
    customSections: yup.array().of(
      yup.object().shape({
        name: yup.string().required("name is required")
      })
    )
  }),
})

const noteTemplate = ref<CreateNoteTemplateCommand>({
  name: "",
  classification: "",
  specialityCode: "",
  documentType: "",
  fields: [],
});

const classifications = [...new Set(specialtiesCodes.map(s => s.classification))]
  .sort()
  .map(c => ({label: c, value: c}));
const specializations = ref<{ label: string, value: string }[]>([]);
const selectedClassification = ref("");

watch(selectedClassification, (newVal) => {
  specializations.value = specialtiesCodes
    .filter(s => s.classification === newVal && s.specialization)
    .map(s => ({label: s.specialization!, value: s.specialization!}));
});

const documentTypes = [
  "Care Plan",
  "Consultation Note",
  "Continuity of Care Document (CCD)",
  "Discharge Summary",
  "History and Physical (H&P)",
  "Operative Note",
  "Procedure Note",
  "Progress Note",
  "Referral Note",
  "Transfer Summary",
  "Unstructured Document"
];

const customSections = ref<{ name: string; content: string; required: boolean }[]>([]);

const selectedDocumentType = ref("");
const sectionTexts = ref<Record<string, string>>({});
const selectedOptionalSections = ref<string[]>([]);
const selectedRequiredAlternatives = ref<Record<number, string>>({});

const locations = ref<SelectListItem[]>([]);
const selectedLocations = ref<string[]>([]);

const requiredSections = computed(() =>
  selectedDocumentType.value
    ? documentSectionsMap[selectedDocumentType.value]?.required ?? []
    : []
);

const recommendedSections = computed(() =>
  selectedDocumentType.value
    ? documentSectionsMap[selectedDocumentType.value]?.recommended ?? []
    : []
);

const additionalSections = computed(() =>
  selectedDocumentType.value
    ? documentSectionsMap[selectedDocumentType.value]?.additional ?? []
    : []
);

const goToNoteTemplatesList = async () => {
  await router.push({name: 'note-templates'})
}

const submitForm = handleSubmit(async (values) => {
  try {
    const fields: NoteTemplateFieldRequest[] = [];

    // Required Sections
    for (const section of requiredSections.value) {
      if (typeof section === 'string') {
        fields.push({name: section, value: sectionTexts.value[section] ?? '', isRequired: true});
      } else if (Array.isArray(section)) {
        if (Array.isArray(section[1])) {
          const selected = selectedRequiredAlternatives.value[requiredSections.value.indexOf(section)];
          if (selected === 'both') {
            for (const name of section[1]) {
              fields.push({name, value: sectionTexts.value[name] ?? '', isRequired: true});
            }
          } else if (selected === section[0]) {
            fields.push({name: section[0], value: sectionTexts.value[section[0]] ?? '', isRequired: true});
          }
        } else {
          const selected = selectedRequiredAlternatives.value[requiredSections.value.indexOf(section)];
          fields.push({name: selected, value: sectionTexts.value[selected] ?? '', isRequired: true});
        }
      }
    }

    // Optional Sections
    for (const section of selectedOptionalSections.value) {
      fields.push({name: section, value: sectionTexts.value[section] ?? '', isRequired: false});
    }

    // Custom Sections
    for (const custom of customSections.value) {
      if (custom.name.trim()) {
        fields.push({name: custom.name.trim(), value: custom.content ?? '', isRequired: custom.required});
      }
    }

    const matchedSpecialty = specialtiesCodes.find(s =>
      s.classification === values.classification &&
      s.specialization === values.specialization
    ) ?? specialtiesCodes.find(s =>
      s.classification === values.classification &&
      !s.specialization
    );
    console.log(values);
    console.log(values.locations);
    const specialityCode = matchedSpecialty?.code;
    const payload: CreateNoteTemplateCommand = {
      name: values.name,
      classification: values.classification,
      specialization: values.specialization,
      specialityCode,
      documentType: values.documentType,
      fields,
      locationIds: values.locations ? values.locations : []
    };

    await api.noteTemplates.noteTemplateCreateNoteTemplate(payload);
    await goToNoteTemplatesList();
  } catch (error) {
    console.error("Error submitting form:", error);
    toast.error("An error occurred while saving.");
  }
});

const fetchLocations = async () => {
  locations.value = (await api.locations.locationListLocationsLookup()).data;
}

watchEffect(async () => {
  if (route.params.id) {
    isEditMode.value = true;
    //await loadQuestionnaire(route.params.id as string);
  }
  if (isOrganizationAdmin.value) {
    await fetchLocations();
  }
});
</script>
