<template>
  <div class="organization-page">
    <h2 class="font-semibold text-4xl leading-10 text-grey-800 px-4 py-4 sm:px-6 lg:px-8 lg:py-4">
      Note Templates
    </h2>
    <TableSection>
      <TableHeader>
        <template #inputs>
          <div class="relative max-w-xs">
            <label class="sr-only">Search</label>
            <input
              @input="filter()"
              type="text"
              class="py-2 px-3 ps-9 block w-full border border-gray-200 shadow-sm rounded-lg text-sm focus:z-10 focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none"
              placeholder="Search"
              v-model="search"
            />
            <div class="absolute inset-y-0 start-0 flex items-center pointer-events-none ps-3">
              <i class="pi pi-search text-gray-400"></i>
            </div>
          </div>
        </template>
        <template #buttons>
          <Select
            v-model="filterSelected"
            :options="filterOptions"
            optionLabel="text"
            option-value="value"
            placeholder="Filter"
            class="w-full md:w-56"
            @change="filter"
          />
          <a
            class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-primary text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none"
            href="#"
            @click.prevent="router.push({ name: 'add-note-template' })"
          >
            <PlusIcon class="shrink-0 w-4 h-4" />
            Create custom template
          </a>
        </template>
      </TableHeader>
      <!-- Table -->
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
        <tr>
          <th scope="col" class="ps-6 py-3 text-start">
            <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Classification
                </span>
            </div>
          </th>
          <th scope="col" class="ps-6 py-3 text-start">
            <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Specialization
                </span>
            </div>
          </th>
          <th scope="col" class="ps-6 py-3 text-start">
            <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Document type
                </span>
            </div>
          </th>
          <th scope="col" class="ps-6 py-3 text-start">
            <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Name
                </span>
            </div>
          </th>
          <th scope="col" class="ps-6 py-3 text-start">
            <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Organization
                </span>
            </div>
          </th>
          <th scope="col" class="ps-6 py-3 text-start">
            <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Location
                </span>
            </div>
          </th>
          <th scope="col" class="ps-6 py-3 text-start">
            <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Author
                </span>
            </div>
          </th>
          <th scope="col" class="px-6 py-3 text-end"></th>
        </tr>
        </thead>

        <tbody class="divide-y divide-gray-200">
        <tr v-for="row in itemList" :key="row.id">
          <td class="h-px w-72 whitespace-nowrap">
            <div class="px-6 py-3">
                <span class="block text-sm font-semibold text-gray-800">{{
                    row.classification
                  }}</span>
            </div>
          </td>

          <td class="h-px w-72 whitespace-nowrap">
            <div class="px-6 py-3">
              <span class="block text-sm font-semibold text-gray-500">{{ row.specialization }}</span>
            </div>
          </td>

          <td class="h-px w-72 whitespace-nowrap">
            <div class="px-6 py-3">
                <span class="block text-sm text-gray-500">{{
                    row.documentType
                  }}</span>
            </div>
          </td>

          <td class="h-px w-72 whitespace-nowrap">
            <div class="px-6 py-3">
                <span class="block text-sm text-gray-500">{{
                    row.name
                  }}</span>
            </div>
          </td>

          <td class="h-px w-72 whitespace-nowrap">
            <div class="px-6 py-3">
                <span class="block text-sm text-gray-500">{{
                    row.organization
                  }}</span>
            </div>
          </td>

          <td class="h-px w-72 whitespace-nowrap">
            <div class="px-6 py-3">
                <span class="block text-sm text-gray-500">{{
                    row.locations
                  }}</span>
            </div>
          </td>

          <td class="h-px w-72 whitespace-nowrap">
            <div class="px-6 py-3">
                <span class="block text-sm text-gray-500">{{
                    row.creator
                  }}</span>
            </div>
          </td>

          <td class="h-px w-72 whitespace-nowrap px-6 py-1.5">
            <div class="flex items-right gap-x-4">
              <a

                  class="inline-flex items-center gap-x-1 text-sm text-primary font-medium hover:underline focus:outline-none focus:underline"
                  href="#"
                  @click.prevent="router.push({ name: 'edit-note-template', params: { id: row.id } })"
              >
                Edit
              </a>
            </div>
          </td>
        </tr>
        </tbody>
      </table>
      <!-- End Table -->

      <TableFooter
        :totalItems="totalItems"
        :isFirstPage="isFirstPage"
        :isLastPage="isLastPage"
        @prevPage="prevPage"
        @nextPage="nextPage"
      />
    </TableSection>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { api } from '@/api'
import type {NoteTemplateResponse} from '@/api/api-reference.ts'
import {PlusIcon} from '@heroicons/vue/24/outline'
import TableHeader from '../../components/table/TableHeader.vue'
import TableFooter from '../../components/table/TableFooter.vue'
import TableSection from '../../components/table/TableSection.vue'
import router from '../../router'
import debounce from "lodash.debounce";
import Select from "primevue/select";
import { specialtiesCodes } from '@/utils/specialties';

const filterOptions = ref([
  { text: "All", value: "all" },
  ...specialtiesCodes.map(s => ({
    text: s.specialization
      ? `${s.classification} – ${s.specialization}`
      : s.classification,
    value: s.code
  }))
]);

const filterSelected = ref('all')
const limit = 10
const itemList = ref<NoteTemplateResponse[]>()
const pageNumber = ref(1)
const totalPages = ref(1)
const totalItems = ref(0)
const search = ref('')

onMounted(async () => {
  await fetchData()
})

const isFirstPage = computed(() => pageNumber.value === 1)
const isLastPage = computed(() => pageNumber.value === totalPages.value)

const nextPage = async () => {
  if (!isLastPage.value) {
    pageNumber.value++
    await fetchData()
  }
}

const prevPage = async () => {
  if (!isFirstPage.value) {
    pageNumber.value--
    await fetchData()
  }
}

const fetchData = async () => {
  try {
    const result = await api.noteTemplates.noteTemplateListNoteTemplates({
      pageNumber: pageNumber.value,
      pageSize: limit,
      searchParam: search.value,
      specialtyFilter: filterSelected.value == "all" ? "" : filterSelected.value
    })
    itemList.value = result.data.items
    pageNumber.value = result.data.pageNumber ?? 1
    totalPages.value = result.data.totalPages ?? 1
    totalItems.value = result.data.totalItems ?? 0
  } catch (error) {
    console.error('Error fetching questionnaires:', error)
  }
}

const filter = debounce(() => {
  fetchData()
}, 500)

</script>
