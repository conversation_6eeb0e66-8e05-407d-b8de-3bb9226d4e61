<template>
  <div class="py-3 space-y-4">
    <div
      v-for="question in props.questionnaire?.questions"
      :key="question.id"
      class="p-4 border rounded-lg bg-gray-50"
    >
      <p class="font-semibold text-gray-900">
        {{ question.text }}
        <span v-if="question.isRequired" v-tooltip.top="'Required'" class="text-red-500">*</span>
      </p>

      <!-- FreeText Dropdown -->
      <InputText v-if="question.type === 'FreeText'" :id="question.id!" label="" />

      <!-- SingleChoice Dropdown -->
      <Select
        v-if="question.type === 'SingleChoice' && question.options"
        :id="question.id!"
        label="Choose option"
        :options="question.options"
        optionLabel=""
        optionValue=""
      />

      <!-- MultipleChoices Checkboxes -->
      <MultiSelect
        v-if="question.type === 'MultipleChoices' && question.options"
        :id="question.id!"
        :options="question.options"
        :maxSelectedLabels="3"
        filter
        label="Choose options"
        optionLabel=""
        optionValue=""
        fluid
      />
    </div>
  </div>
  <div class="card flex justify-end mb-4">
    <Button label="Save" @click="submitAnswers" />
  </div>
</template>

<script setup lang="ts">
import { defineProps, ref, onBeforeMount, watchEffect } from 'vue'
import { api } from '@/api'
import type {PatientQuestionnaireResponse, QuestionResponse2} from '@/api/api-reference.ts'
import Button from 'primevue/button'
import MultiSelect from '@/components/form-extensions/MultiSelectFluent.vue'
import InputText from '@/components/form-extensions/InputTextFluent.vue'
import { useForm } from 'vee-validate'
import * as yup from 'yup'
import Select from '@/components/form-extensions/SelectFluent.vue'
import {useToast} from "vue-toastification";

const props = defineProps<{ questionnaire: PatientQuestionnaireResponse | undefined, encounterId: string }>()
const answers = ref<Record<string, string | string[]>>({})
const toast = useToast()

onBeforeMount(async () => {
  initializeAnswers()
})

const emit = defineEmits<{
  (e: 'submitted', id: string): void
}>()

function buildValidationSchema(questions: QuestionResponse2[]) {
  const shape: Record<string, yup.AnySchema> = {}

  for (const question of questions) {
    let schema: yup.AnySchema

    switch (question.type) {
      case 'FreeText':
        schema = yup.string()
        break
      case 'SingleChoice':
        schema = yup.string().oneOf(question.options ?? [])
        break
      case 'MultipleChoices':
        schema = yup.array().of(yup.string().oneOf(question.options ?? []))
        break
      default:
        schema = yup.string()
        break
    }

    if (question.isRequired) {
      schema = schema.required('This field is required')
    } else {
      schema = schema.nullable()
    }

    shape[question.id!] = schema
  }

  return yup.object().shape(shape)
}

const validationSchema = buildValidationSchema(props.questionnaire?.questions! ?? [])

const { handleSubmit, setFieldValue } = useForm({
  validationSchema,
})

watchEffect(async () => {})

// Initialize answers
const initializeAnswers = () => {
  props.questionnaire?.questions!.forEach((q) => {
    switch (q.type) {
      case 'FreeText':
        setFieldValue(q.id!, q.answers![0])
        break
      case 'SingleChoice':
        setFieldValue(q.id!, q.answers![0])
        break
      case 'MultipleChoices':
        setFieldValue(q.id!, q.answers)
        break
    }
  })
}

const handleMultipleChoice = (questionId: string, option: string) => {
  const selectedOptions = answers.value[questionId] as string[]
  if (selectedOptions.includes(option)) {
    answers.value[questionId] = selectedOptions.filter((o) => o !== option)
  } else {
    selectedOptions.push(option)
  }
}

const submitAnswers = handleSubmit(async (values) => {
  try {
    const answers = Object.entries(values)
      .filter(([_, answer]) => {
        const normalized = Array.isArray(answer) ? answer : [answer]
        return (
          normalized.length > 0 && normalized.some((a) => a !== undefined && a !== null && a !== '')
        )
      })
      .map(([questionId, answer]) => ({
        questionId,
        answers: Array.isArray(answer) ? answer : [answer],
      }))
    const data = {
      questionnaireId: props.questionnaire?.id,
      answers: answers,
      encounterId: props.encounterId
    };
    await api.questionnaires.questionnaireSaveResponse(data);
    emit('submitted', props.questionnaire?.id!)
    toast.success('Your responses have been saved successfully.')
  } catch (error) {
    console.error('Error updating contact information:', error)
    toast.error("There was a problem saving your responses. Please try again.")
  }
})
</script>
