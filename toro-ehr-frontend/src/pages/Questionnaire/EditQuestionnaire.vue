<template>
  <div class="p-4 sm:p-5">
    <Card>
      <template #title> {{ isEditMode ? "Edit Questionnaire" : "Create Questionnaire" }} </template>
      <template #content>
        <div class="p-fluid">
          <!-- Title & Location in one row -->
          <div class="flex flex-col sm:flex-row gap-4 mb-4">
            <div class="w-full sm:w-1/3">
              <InputText id="title" label="Title" class="w-full" />
            </div>
            <div class="w-full sm:w-1/3">
            <InputText id="type" label="Type" class="w-full" />
          </div>
            <div class="w-full sm:w-1/3">
              <Select id="placement" :options="placements" optionLabel="label" label="Placement" optionValue="value" class="w-full" />
            </div>
          </div>

          <div class="mt-4">
            <h3 class="text-lg font-bold mb-2">Questions</h3>
            <div v-for="(question, index) in questionnaire.questions" :key="index" class="relative border p-3 rounded-lg mb-2">
              <InputText :id="'questions['+index+'].id'" v-model="question.id!" class="hidden" label="" />

              <!-- Question title & type in one row -->
              <div class="flex flex-col sm:flex-row gap-4 items-start sm:items-end mb-3">
                <div class="w-full sm:w-5/12">
                  <InputText :id="'questions['+index+'].text'" label="Text" class="w-full" />
                </div>
                <div class="w-full sm:w-5/12">
                  <Select :id="'questions['+index+'].type'" v-model="question.type" :options="questionTypes" optionLabel="label" optionValue="value" label="Type" class="w-full" />
                </div>
                <div class="w-full sm:w-2/12 mb-[1.75rem] flex items-center gap-2">
                  <Checkbox :inputId="'questions[' + index + '].isRequired'" v-model="question.isRequired" binary />
                  <label :for="'questions[' + index + '].isRequired'">Required</label>
                </div>
              </div>

              <!-- Choices for Single/Multiple Choice -->
              <div v-if="question.type === 'SingleChoice' || question.type === 'MultipleChoices'">
                <h4 class="text-md font-bold mt-2">Options</h4>
                <div v-for="(option, optionIndex) in question.options" :key="optionIndex" class="flex items-center space-x-2 mb-2">
                  <InputText :id="'questions['+index+'].options['+optionIndex+']'" label="Option" class="w-full" />
                  <i
                    class="pi pi-trash text-red-500 cursor-pointer text-lg"
                    @click="removeOption(question, optionIndex)"
                  ></i>
                </div>
                <div class="flex justify-end mt-2">
                  <Button icon="pi pi-plus-circle" label="Add Option" variant="text"
                          class="text-blue-600 flex items-center gap-1 font-medium" @click="addOption(question)"/>
                </div>
              </div>
              <div class="flex justify-end gap-2 mt-2">
                <i
                  v-tooltip.top="'Duplicate question'"
                  class="pi pi-copy text-blue-500 cursor-pointer text-lg p-1 hover:bg-gray-100 rounded-full transition"
                  @click="duplicateQuestion(index)"
                ></i>
                <i
                  v-tooltip.top="'Delete question'"
                  class="pi pi-trash text-red-500 cursor-pointer text-lg p-1 hover:bg-gray-100 rounded-full transition"
                  @click="removeQuestion(index)"
                ></i>
              </div>
            </div>

            <div class="flex justify-end mt-2">
              <Button icon="pi pi-plus-circle" label="Add question" variant="text"
                      class="text-blue-600 flex items-center gap-1 font-medium" @click="addQuestion"/>
            </div>
          </div>


          <div class="card flex flex-col sm:flex-row justify-end gap-2 sm:gap-4 mb-2 mt-6">
            <Button class="px-4 py-2" label="Close" severity="secondary" variant="text" @click="goToQuestionnaireList()" />
            <Button label="Save" @click="submitForm" class="" />
          </div>
        </div>
      </template>
    </Card>
  </div>
</template>

<script setup lang="ts">
import InputText from "@/components/form-extensions/InputTextFluent.vue";
import Select from "@/components/form-extensions/SelectFluent.vue";
import Checkbox from 'primevue/checkbox';
import {ref, watchEffect} from "vue";
import type {
  CreateQuestionnaireCommand,
  QuestionRequest, UpdateQuestionnaireCommand
} from "@/api/api-reference.ts";
import Button from "primevue/button";
import { api } from '@/api'
import Card from 'primevue/card';
import { useToast } from 'vue-toastification'
import {useForm} from "vee-validate";
import * as yup from "yup";
import {useRoute, useRouter} from "vue-router";

const toast = useToast()
const route = useRoute();
const router = useRouter();

const isEditMode = ref(false);

const { handleSubmit, setFieldError, setValues } = useForm({
  validationSchema: yup.object({
    title: yup.string().required(),
    type: yup.string().required(),
    placement: yup.string().required(),
    questions: yup.array().of(
      yup.object().shape({
        text: yup.string().required("text is required"),
        type: yup.string().required("type is required"),
        options: yup.array().of(yup.string().required("Option is required"))
      })
    )
  }),
})

const questionnaire = ref<CreateQuestionnaireCommand | UpdateQuestionnaireCommand>({
  id: "",
  title: "",
  placement: "",
  questions: [],
});

const goToQuestionnaireList = async () => {
  await router.push({ name: 'questionnaires' })
}

async function loadQuestionnaire(id: string) {
  try {
    const response = await api.questionnaires.questionnaireGetQuestionnaire(id);
    if (route.name === 'copy-questionnaire'){
      questionnaire.value = {...response.data, title: "Copy of - " + response.data.title};
    }
    else {
      questionnaire.value = response.data
    }
    setValues(questionnaire.value);
  } catch (error) {
    console.error("Error loading questionnaire:", error);
  }
}

const placements = [
  { label: "Profile", value: "Profile" },
  { label: "Check In", value: "CheckIn" },
];

const questionTypes = [
  { label: "Free Text", value: "FreeText" },
  { label: "Single Choice", value: "SingleChoice" },
  { label: "Multiple Choices", value: "MultipleChoices" },
];

const addQuestion = () => {
  questionnaire.value.questions!.push({ text: "", type: "", options: [] });
};

const removeQuestion = (index: number) => {
  questionnaire.value.questions!.splice(index, 1);
};

const addOption = (question: QuestionRequest) => {
  question.options!.push("");
};

const removeOption = (question: QuestionRequest, optionIndex: number) => {
  question.options!.splice(optionIndex, 1);
};

const duplicateQuestion = (index: number) => {
  const questionToCopy = questionnaire.value.questions![index];
  const copiedQuestion = {
    ...questionToCopy,
    id: "",
  };
  questionnaire.value.questions!.push(copiedQuestion);
};

const submitForm = handleSubmit(async (values) => {
  try {
    if (route.name === 'add-questionnaire') {
      await api.questionnaires.questionnaireCreate(values)
    }
    else {
      await api.questionnaires.questionnaireUpdate(values)
    }
    await goToQuestionnaireList();
  }
  catch (error) {
    //setFieldError("title", error.errors.Title[0])
    console.log(error)
    /*Object.values(error.errors).forEach(messages => {
      messages.forEach(message => {
        toast.error(message);
      });
    });*/
  }
});

watchEffect(async () => {
  if (route.params.id) {
    isEditMode.value = true;
    await loadQuestionnaire(route.params.id as string);
  }
});
</script>
