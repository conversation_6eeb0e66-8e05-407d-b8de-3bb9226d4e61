<template>
  <form @submit.prevent="setPersonalInfo">
    <InputText id="firstName" label="First Name"/>
    <InputText id="middleName" label="Middle Name"/>
    <InputText id="lastName" label="Last Name"/>
    <InputText id="suffix" label="Suffix"/>
    <InputText id="preferredName" label="Preferred name (if different)"/>

    <InputText id="previousFirstName" label="Previous First Name"/>
    <InputText id="previousLastName" label="Previous Last Name"/>
    <Select id="preferredLanguage" label="Preferred language" :options="languages"/>
    <div class="relative">
      <div class="my-3">
        <FloatLabel variant="on">
          <DatePicker id="birthday" v-model="birthday"/>
          <label for="birthday">Birthday</label>
        </FloatLabel>
      </div>
    </div>

    <div class="sm:flex sm:flex-col sm:gap-2 lg:grid lg:grid-cols-3 lg:gap-4">
      <div class="relative">
        <div class="relative">
          <label class="absolute px-1 bg-white text-sm font-medium text-gray-700 left-2 -top-2.5">
            Birth Sex
          </label>
          <div class="border border-gray-300 rounded-lg p-2 mt-3">
            <div class="flex flex-wrap gap-4">
              <div class="flex items-center gap-2">
                <RadioButton v-model="birthSex" inputId="Female" name="birthSex" value="Female"/>
                <label for="Female">Female</label>
              </div>
              <div class="flex items-center gap-2">
                <RadioButton v-model="birthSex" inputId="Male" name="birthSex" value="Male"/>
                <label for="Male">Male</label>
              </div>
              <div class="flex items-center gap-2">
                <RadioButton v-model="birthSex" inputId="Other" name="birthSex" value="Other"/>
                <label for="Other">Other</label>
              </div>
            </div>
          </div>
        </div>
        <div>
          <transition name="fade">
            <small v-if="errors.birthSex"
                   class="absolute text-red-500 text-xs right-2 -bottom-5 bg-white px-1">
              {{ errors.birthSex }}
            </small>
          </transition>
        </div>
      </div>
      <Select id="genderIdentity" label="Self-identified gender" :options="genderOptions"/>
      <Select id="sexualOrientation" label="Sexual orientation" :options="orientationOptions"/>
    </div>
    <div class="sm:flex sm:flex-col sm:gap-2 lg:grid lg:grid-cols-3 lg:gap-4">
      <Select id="race" label="Race" :options="races" class="w-full"/>
      <Select id="ethnicity" label="Ethnicity" :options="ethnicities" class="w-full"/>
      <Select id="tribalAffiliation" label="Tribal affiliation" :options="tribalAffiliations" class="w-full"/>
    </div>

    <div class="bg-white lg:p-6 rounded-lg shadow">
      <h2 class="text-xl font-semibold mb-4">Documents</h2>

      <!-- Upload Row (Select + FileUpload + Add Button) -->
      <div class="flex flex-col gap-2 border border-gray-300 rounded-lg p-4">
        <!-- Document Type Select -->
        <div class="w-full">
          <Select id="selectedDocumentType" v-model="selectedDocumentType" label="Document type"
                  :options="availableDocumentTypes" class="w-full"/>
        </div>

        <!-- Front File Upload -->
        <div class="w-full flex items-center gap-2">
          <FileUpload
            mode="basic"
            chooseLabel="Choose Front"
            custom-upload
            auto
            accept="image/*,application/pdf"
            @select="handleFileSelect($event, 'front')"
          />
          <span v-if="selectedFiles.front" class="text-gray-600 text-sm truncate w-40 block">
      {{ selectedFiles.front.name }}
    </span>
          <span v-else class="text-gray-400 text-sm">No front file chosen</span>
        </div>

        <!-- Back File Upload -->
        <div class="w-full flex items-center gap-2">
          <FileUpload
            mode="basic"
            chooseLabel="Choose Back"
            custom-upload
            auto
            accept="image/*,application/pdf"
            @select="handleFileSelect($event, 'back')"
          />
          <span v-if="selectedFiles.back" class="text-gray-600 text-sm truncate w-40 block">
      {{ selectedFiles.back.name }}
    </span>
          <span v-else class="text-gray-400 text-sm">No back file chosen</span>
        </div>

        <!-- Add Button (Bottom Right) -->
        <div class="flex justify-end mt-4">
          <Button label="Add" icon="pi pi-plus" @click="addDocument"
                  class="p-button-primary h-10 px-6"/>
        </div>
      </div>

      <!-- Uploaded Files List -->
      <div class="mt-6">
        <h3 class="text-lg font-semibold mb-2">Uploaded Documents</h3>
        <div v-if="newUploadedDocuments.length === 0 && (serverDocuments?.length ?? 0) === 0" class="text-gray-500 text-sm">No
          documents uploaded yet.
        </div>

        <div v-for="(doc, index) in newUploadedDocuments" :key="index" class="border p-2 rounded-lg mb-2">
          <div class="flex flex-col sm:flex-row items-center gap-2">
            <!-- Iterate Over Both Files (Front & Back) -->
            <div v-for="(file, fileIndex) in doc.documentFiles" :key="fileIndex"
                 class="flex flex-col sm:flex-row items-center border p-2 rounded-lg">
              <!-- File Preview -->
              <div
                class="w-12 h-12 sm:w-16 sm:h-16 border rounded-lg overflow-hidden flex justify-center items-center bg-gray-100">
                <a v-if="file" :href="getFileUrl(file)" target="_blank" rel="noopener noreferrer">
                  <img alt="document" v-if="isImageNew(file)" :src="getFileUrl(file)"
                       class="w-full h-full object-cover"/>
                  <span v-else class="text-gray-500 text-sm">PDF</span>
                </a>
              </div>
            </div>
            <!-- File Details -->
            <div class="mt-2 sm:mt-0 sm:ml-4 flex-1">
              <p class="font-semibold">{{ doc.documentType }}</p>
              <p class="text-gray-500 text-sm truncate max-w-xs">{{ formatDateTime(new Date().toISOString()).formattedDateTime }}</p>
            </div>

            <!-- Delete Button -->
            <Button icon="pi pi-trash" class="p-button-text text-red-500" @click="removeFile(doc)"/>

          </div>
        </div>
        <div v-for="(doc, index) in serverDocuments" :key="index" class="border p-2 rounded-lg mb-2">
          <div class="flex flex-col sm:flex-row items-center gap-2">
            <!-- Iterate Over Both Files (Front & Back) -->
            <div v-for="(file, fileIndex) in doc.filePaths" :key="fileIndex"
                 class="flex flex-col sm:flex-row items-center p-2 rounded-lg">
              <!-- File Preview -->
              <div
                class="w-12 h-12 sm:w-16 sm:h-16 border rounded-lg overflow-hidden flex justify-center items-center bg-gray-100">
                <a v-if="file" :href="file" target="_blank" rel="noopener noreferrer">
                  <img alt="document" v-if="isImageExisting(file)" :src="file" class="w-full h-full object-cover"/>
                  <span v-else class="text-gray-500 text-sm">PDF</span>
                </a>
              </div>
              <!-- File Details -->
              <!--              <div class="mt-2 sm:mt-0 sm:ml-4 flex-1">
                              <p class="font-semibold">{{ doc.documentType }}</p>
                              <p class="text-gray-500 text-sm truncate max-w-xs">{{ getFileName(file) }}</p>
                            </div>-->
            </div>
            <!-- File Details -->
            <div class="mt-2 sm:mt-0 sm:ml-4 flex-1">
              <p class="font-semibold">{{ doc.documentType }}</p>
              <p class="text-gray-500 text-sm truncate max-w-xs">{{
                  formatDateTime(doc.createdAt ?? '').formattedDateTime
                }}</p>
            </div>

            <!-- Delete Button -->
            <Button icon="pi pi-trash" class="p-button-text text-red-500" @click="removeFile(doc)"/>

          </div>
        </div>
      </div>
    </div>

    <Divider/>
    <div class="card flex justify-end mb-4">
      <Button label="Save" type="submit"/>
    </div>
  </form>
</template>

<script setup lang="ts">
import {ref, watch, computed, watchEffect} from "vue";
import InputText from "@/components/form-extensions/InputTextFluent.vue";
import RadioButton from 'primevue/radiobutton';
import DatePicker from 'primevue/datepicker';
import FloatLabel from "primevue/floatlabel";
import Select from '../../components/form-extensions/SelectFluent.vue'
import Divider from 'primevue/divider';
import Button from 'primevue/button';
import {getLanguages} from "@/utils/languages.ts";
import {getRaces} from "@/utils/races.ts";
import {getEthnicities} from "@/utils/ethnicities.ts";
import type {DocumentResponse, PatientDocument} from "@/api/api-reference.ts";
import {api} from "@/api";
import {useField, useForm} from "vee-validate";
import * as yup from "yup";
import FileUpload from 'primevue/fileupload';
import {usePatientStore} from "@/stores/patient.ts";
import {useAuthStore} from "@/stores/auth.ts";
import {getTribalAffiliations} from "@/utils/tribalAffiliations.ts";
import {useToast} from "vue-toastification";

const patientStore = usePatientStore();
const authStore = useAuthStore();
const toast = useToast();

/* select values */
const languages = getLanguages()
const races = [{text: "Not specified", value: ""}, ...getRaces()]
const ethnicities = [{text: "Not specified", value: ""}, ...getEthnicities()]
const tribalAffiliations = [{text: "Not specified", value: ""}, ...getTribalAffiliations()]

const genderOptions = ref([
  {text: "Female", value: "Female"},
  {text: "Male", value: "Male"},
  {text: "Transwoman", value: "Transwoman"},
  {text: "Transman", value: "Transman"},
  {text: "Non-binary", value: "Non-binary"},
  {text: "Prefer not to say", value: "Prefer not to say"}
]);

const orientationOptions = ref([
  {text: "Asexual", value: "Asexual"},
  {text: "Bisexual", value: "Bisexual"},
  {text: "Heterosexual", value: "Heterosexual"},
  {text: "Homosexual", value: "Homosexual"},
  {text: "Prefer not to say", value: "Prefer not to say"}
]);

const documentTypes = [
  {text: "Identification Card", value: "IdentificationCard"},
  {text: "Passport", value: "Passport"},
  {text: "Driver's License", value: "DriversLicense"},
  {text: "Student ID Card", value: "StudentIDCard"},
  {text: "Birth Certificate", value: "BirthCertificate"},
  {text: "Citizenship Card/Certificate", value: "CitizenshipCardCertificate"},
  {text: "Diplomatic Passport", value: "DiplomaticPassport"},
  {text: "Other Government Issued License Card", value: "OtherGovernmentIssuedLicenseCard"},
  {text: "Medicare Card", value: "MedicareCard"},
  {text: "Medicaid Card", value: "MedicaidCard"},
  {text: "Marriage Certificate", value: "MarriageCertificate"},
  {text: "Military ID", value: "MilitaryID"},
  {text: "Naturalization Certificate", value: "NaturalizationCertificate"},
  {text: "Government Employee Identification Card", value: "GovernmentEmployeeIdentificationCard"},
  {text: "Parole Card", value: "ParoleCard"},
  {text: "Social Security Card", value: "SocialSecurityCard"},
  {text: "Government Issued Visitor Permit", value: "GovernmentIssuedVisitorPermit"},
  {text: "Government Issued Work Permit", value: "GovernmentIssuedWorkPermit"},
  {text: "Other ID", value: "OtherID"}
];

// computed property to filter out already used document types
const availableDocumentTypes = computed(() => {
  const usedTypes = new Set();

  // add types from server documents
  serverDocuments.value?.forEach(doc => {
    usedTypes.add(doc.documentType);
  });

  // add types from newly uploaded documents
  newUploadedDocuments.value.forEach(doc => {
    usedTypes.add(doc.documentType);
  });

  return documentTypes.filter(docType => !usedTypes.has(docType.text));
});

const {handleSubmit, setValues, setFieldValue, errors} = useForm({
  validationSchema: yup.object({
    firstName: yup.string().required('First name is required'),
    lastName: yup.string().required('Last name is required'),
    birthday: yup.string().required('Birthday is required'),
    birthSex: yup.string().required('Birth sex is required')
  }),
})

const {value: birthSex} = useField<string | null>('birthSex')
const {value: birthday} = useField<Date | null>('birthday')

const setPersonalInfo = handleSubmit(async (values) => {
  try {
    const formData = new FormData();

    // Append all text fields
    formData.append("patientId", authStore.user?.patientId ?? "");
    formData.append("firstName", values.firstName);
    formData.append("lastName", values.lastName);
    formData.append("middleName", values.middleName ?? "");
    formData.append("suffix", values.suffix ?? "");
    formData.append("preferredName", values.preferredName ?? "");
    formData.append("previousFirstName", values.previousFirstName ?? "");
    formData.append("previousLastName", values.previousLastName ?? "");
    formData.append("preferredLanguage", values.preferredLanguage ?? "");
    formData.append("birthday", values.birthday.toISOString());
    formData.append("birthSex", values.birthSex);
    formData.append("genderIdentity", values.genderIdentity ?? "");
    formData.append("sexualOrientation", values.sexualOrientation ?? "");
    formData.append("race", values.race ?? "");
    formData.append("ethnicity", values.ethnicity ?? "");
    formData.append("tribalAffiliation", values.tribalAffiliation ?? "");
    formData.append("deleteDocumentIds", JSON.stringify(documentsForDeletion.value.length > 0 ? documentsForDeletion : []));

    // Append documents separately
    if (newUploadedDocuments.value.length > 0) {
      newUploadedDocuments.value.forEach((doc, index) => {
        if (doc.documentFiles && doc.documentFiles.length > 0) {
          doc.documentFiles.forEach((fileObj) => {
            formData.append(`newDocuments[${index}].documentFiles`, fileObj); // ✅ No index here
          });
        } else {
          // Ensure documentFiles is present as an empty array if no files exist
          formData.append(`newDocuments[${index}].documentFiles`, new Blob());
        }

        // Ensure documentType is always sent
        formData.append(`newDocuments[${index}].documentType`, doc.documentType || "");
      });
    } else {
      // ✅ Ensure newDocuments is sent as an empty array
      formData.append("newDocuments", JSON.stringify([]));
    }
    await api.patients.patientSetPersonalInformation(formData);
    await patientStore.getPatientProfile();

    // clear local documents since they're now saved on server
    newUploadedDocuments.value = [];
    documentsForDeletion.value = [];

    toast.success("Personal information updated successfully");
  } catch (error) {
    console.log(error)
    toast.error("Failed to update personal information");
  }
})

const selectedDocumentType = ref();
const selectedFiles = ref<{ front: File | null; back: File | null }>({
  front: null,
  back: null
});

const serverDocuments = ref<DocumentResponse[] | undefined>([]);
const newUploadedDocuments = ref<PatientDocument[]>([]);
const documentsForDeletion = ref<string[]>([]);

const handleFileSelect = (event: any, side: 'front' | 'back') => {
  console.log(event.files[0])
  console.log(side)
  if (event.files.length > 0) {
    selectedFiles.value[side] = event.files[0];
  }
};

const addDocument = () => {
  if (!selectedDocumentType.value) {
    toast.error("Please select a document type!");
    return;
  }

  if (!selectedFiles.value.back && !selectedFiles.value.front) {
    toast.error("Please select a file!");
    return;
  }

  const selectedDocumentText = documentTypes.find(d => d.value === selectedDocumentType.value)?.text || "Unknown";

  console.log("Adding document with files:", selectedFiles.value.front, selectedFiles.value.back);

  // ✅ Ensure only NEW documents are added for upload
  newUploadedDocuments.value.push({
    documentType: selectedDocumentText,
    documentFiles: [
      selectedFiles.value.front && selectedFiles.value.front instanceof File ? selectedFiles.value.front : null,
      selectedFiles.value.back && selectedFiles.value.back instanceof File ? selectedFiles.value.back : null
    ].filter((file): file is File => file !== null)
  });

  console.log("Updated newUploadedDocuments:", JSON.stringify(newUploadedDocuments.value, null, 2));

  // Reset file input and selection
  selectedDocumentType.value = null;
  selectedFiles.value = {front: null, back: null};
};

const removeFile = (doc: DocumentResponse | PatientDocument) => {
  if ("docId" in doc) {
    // ✅ It's a DocumentResponse
    serverDocuments.value = serverDocuments.value!.filter(d => d.docId !== doc.docId);
    documentsForDeletion.value.push(doc.docId!);
  } else {
    // ✅ It's a PatientDocument
    newUploadedDocuments.value = newUploadedDocuments.value.filter(d => d !== doc);
  }
};

const isImageNew = (file: File) => {
  return file.type.startsWith("image/");
};

const isImageExisting = (filePath: string) => {
  return filePath.endsWith(".jpg") || filePath.endsWith(".png") || filePath.endsWith(".jpeg")
};

const getFileName = (filePath: string) => {
  return filePath ? filePath.split("/").pop() : "Unknown";
};

const getFileUrl = (file: File) => {
  return URL.createObjectURL(file);
};

const formatDateTime = (dateTimeOffset: string) => {
  const date = new Date(dateTimeOffset);

  const formattedDate = date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  });

  const formattedTime = date.toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: true,
  });

  const formattedDateTime = `${formattedDate}, ${formattedTime}`;

  return {formattedDate, formattedTime, formattedDateTime};
};

watchEffect(() => {
  const patientPersonalInfo = {
    firstName: patientStore.patientProfile?.firstName,
    lastName: patientStore.patientProfile?.lastName,
    middleName: patientStore.patientProfile?.middleName,
    suffix: patientStore.patientProfile?.suffix,
    preferredName: patientStore.patientProfile?.preferredName,
    previousFirstName: patientStore.patientProfile?.previousFirstName,
    previousLastName: patientStore.patientProfile?.previousLastName,
    preferredLanguage: patientStore.patientProfile?.preferredLanguage,
    birthday: new Date(patientStore.patientProfile?.birthday ?? ''),
    birthSex: patientStore.patientProfile?.birthSex,
    genderIdentity: patientStore.patientProfile?.genderIdentity,
    sexualOrientation: patientStore.patientProfile?.sexualOrientation,
    race: patientStore.patientProfile?.race,
    ethnicity: patientStore.patientProfile?.ethnicity,
    tribalAffiliation: patientStore.patientProfile?.tribalAffiliation
  }
  setValues(patientPersonalInfo)
  serverDocuments.value = patientStore.patientProfile?.documents;
});
</script>
