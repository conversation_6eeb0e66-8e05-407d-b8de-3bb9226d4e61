<template>
  <div class="p-4 bg-white shadow-md rounded-lg mb-4">
    <label class="text-gray-700 font-medium">Address</label>
    <InputText id="streetAddress" v-model="address.street" label="Street address" class="w-full"/>
    <div class="grid grid-cols-1 sm:grid-cols-3 sm:gap-4">
      <InputText id="city" label="City" v-model="address.city" class="w-full"/>
      <Select id="state" label="State" :options="states" v-model="address.state" class="w-full"/>
      <InputText id="zip" label="Zip" class="w-full" v-model="address.zipCode"/>
    </div>
  </div>
  <div class="p-4 bg-white shadow-md rounded-lg">
    <label class="text-gray-700 font-medium">Previous Address</label>
    <InputText id="previousStreetAddress" v-model="previousAddress.street" label="Street address"/>
    <div class="grid grid-cols-1 sm:grid-cols-3 sm:gap-4">
      <InputText id="previousCity" label="City" v-model="previousAddress.city"/>
      <Select id="previousState" label="State" :options="states" v-model="previousAddress.state"/>
      <InputText id="previousZip" label="Zip" v-model="previousAddress.zipCode"/>
    </div>
  </div>
  <div class="grid grid-cols-1 sm:grid-cols-3 sm:gap-4 mt-2">
    <InputText id="socialSecurityNumber" label="Social Security Number" v-model="socialSecurityNumber" />
    <Select id="preferredContactMethod" label="Preferred contact method" :options="contactMethods" v-model="preferredContactMethod"/>
    <InputText id="preferredContactName" label="Preferred contact name" v-model="preferredContactName"/>
  </div>
  <!-- Emails Section -->
  <div class="p-4 mb-4 bg-white shadow-md rounded-lg">
    <label class="text-gray-700 font-medium">Emails</label>
    <div
      v-for="(emailAddress, index) in emails"
      :key="'email-' + index"
      class="flex flex-wrap items-center gap-2 sm:gap-4 px-4 py-2 border rounded-lg bg-gray-50 mt-2"
    >
      <InputText id="email" v-model="emailAddress.email" label="Email" class="w-full sm:flex-1"/>
      <div class="flex items-center gap-2">
        <Checkbox v-model="emailAddress.isPrimary" :binary="true" :disabled="hasPrimary && !emailAddress.isPrimary"
                  input-id="primaryEmail"/>
        <label for="primaryEmail" class="text-gray-700">Primary</label>
      </div>
      <i
        v-if="emails.length > 1"
        class="pi pi-trash text-red-500 cursor-pointer text-lg"
        @click="removeEmail(index)"
      ></i>
    </div>
    <div class="flex justify-end mt-2">
      <Button icon="pi pi-plus-circle" label="Add email" variant="text" :disabled="!isLastEmailFilled"
              class="text-blue-600 flex items-center gap-1 font-medium" @click="addEmail"/>
    </div>
  </div>
  <!-- Phones Section -->
  <div class="p-4 mb-4 bg-white shadow-md rounded-lg">
    <label class="text-gray-700 font-medium">Phone Numbers</label>

    <div
      v-for="(phoneObj, index) in phoneNumbers"
      :key="'phone-' + index"
      class="flex flex-wrap items-center gap-2 sm:gap-4 px-4 py-2 border rounded-lg bg-gray-50 mt-2"
    >
      <InputText id="phone" v-model="phoneObj.number" label="Phone number" class="w-full sm:flex-1"/>
      <Select id="phoneType" v-model="phoneObj.type" :options="phoneTypes" label="Phone type" class="w-full sm:flex-1"/>
      <div class="flex items-center gap-2">
        <Checkbox v-model="phoneObj.isPrimary" :binary="true" :disabled="hasPrimaryPhone && !phoneObj.isPrimary"
                  input-id="primaryPhoneNumber"/>
        <label for="primaryPhoneNumber" class="text-gray-700">Primary</label>
      </div>
      <i
        v-if="phoneNumbers.length > 1"
        class="pi pi-trash text-red-500 cursor-pointer text-lg"
        @click="removePhoneNumber(index)"
      ></i>
    </div>
    <div class="flex justify-end mt-2">
      <Button icon="pi pi-plus-circle" label="Add phone" variant="text" :disabled="!isLastPhoneFilled"
              class="text-blue-600 flex items-center gap-1 font-medium" @click="addPhoneNumber"/>
    </div>
  </div>
  <!-- Emergency Contacts Section -->
  <div class="p-4 bg-white shadow-md rounded-lg">
    <label class="text-gray-700 font-medium">Emergency Contacts</label>

    <div
      v-for="(contact, index) in contacts"
      :key="'contact-' + index"
      class="flex flex-wrap items-center gap-2 sm:gap-4 px-4 py-2 border rounded-lg bg-gray-50 mt-2"
    >
      <InputText id="contact.name" v-model="contact.name" label="Name" class="w-full sm:flex-1"/>
      <InputText id="contact.phoneNumber" v-model="contact.phoneNumber" label="Phone number" class="w-full sm:flex-1"/>
      <Select id="contactRelation" v-model="contact.relationship" label="Relation" :options="relations"
              class="w-full sm:flex-1"/>
      <div class="flex items-center gap-2">
        <Checkbox v-model="contact.primary" :binary="true" :disabled="hasPrimaryContact && !contact.primary"
                  input-id="primaryContact"/>
        <label for="primaryContact" class="text-gray-700">Primary</label>
      </div>
      <i
        v-if="contacts.length > 1"
        class="pi pi-trash text-red-500 cursor-pointer text-lg"
        @click="removeContact(index)"
      ></i>
    </div>

    <div class="flex justify-end mt-2">
      <Button icon="pi pi-plus-circle" label="Add contact" variant="text"
              class="text-blue-600 flex items-center gap-1 font-medium"
              :disabled="!isLastContactFilled"
              @click="addContact"/>
    </div>
  </div>
  <Divider/>
  <div class="card flex justify-end mb-4">
    <Button label="Save" @click="setContactInfo"/>
  </div>
</template>

<script setup lang="ts">
import {computed, ref, watchEffect} from 'vue';
import {getAllStates} from "@/utils/states.ts";
import Select from '../../components/form-extensions/SelectFluent.vue'
import InputText from "@/components/form-extensions/InputTextFluent.vue";
import {getContactMethods} from "@/utils/contactMethods.ts";
import Button from "primevue/button";
import Checkbox from 'primevue/checkbox';
import Divider from "primevue/divider";
import type {
  AddressRequest,
  EmailAddressRequest,
  EmergencyContactRequest,
  PhoneNumberRequest
} from "@/api/api-reference.ts";
import {api} from "@/api";
import {usePatientStore} from "@/stores/patient.ts";
import {useToast} from "vue-toastification";

const states = getAllStates()
const contactMethods = getContactMethods()
const relations = [
  {text: "Child", value: "Child"},
  {text: "Mother", value: "Mother"},
  {text: "Father", value: "Father"},
  {text: "Sibling", value: "Sibling"},
  {text: "Spouse", value: "Spouse"},
  {text: "Great grandparents", value: "Great grandparents"},
  {text: "Extended family member", value: "Extended family member"},
  {text: "Friend", value: "Friend"},
  {text: "Neighbor", value: "Neighbor"}
];

/*EMAILS*/
const emails = ref<EmailAddressRequest[]>([{
  email: '',
  isPrimary: false
}]);

const hasPrimary = computed(() => emails.value.some(e => e.isPrimary));
const isLastEmailFilled = computed(() => {
  const lastEmail = emails.value[emails.value.length - 1];
  return lastEmail && lastEmail.email?.trim() !== "";
});
const addEmail = () => {
  emails.value.push({email: '', isPrimary: false});
};
const removeEmail = (index: number) => {
  emails.value.splice(index, 1);
};

/*PHONES*/
const phoneTypes = [
  {text: "Mobile", value: "Mobile"},
  {text: "Home", value: "Home"},
  {text: "Work", value: "Work"},
];

const phoneNumbers = ref<PhoneNumberRequest[]>([{
  number: '',
  type: '',
  isPrimary: false
}]);

const hasPrimaryPhone = computed(() => phoneNumbers.value.some(p => p.isPrimary));
const isLastPhoneFilled = computed(() => {
  const lastPhone = phoneNumbers.value[phoneNumbers.value.length - 1];
  return lastPhone && lastPhone.number?.trim() !== "";
});

const addPhoneNumber = () => {
  phoneNumbers.value.push({number: '', type: '', isPrimary: false});
};

const removePhoneNumber = (index: number) => {
  if (phoneNumbers.value.length > 1) {
    phoneNumbers.value.splice(index, 1);
  }
};

/*EMERGENCY CONTACTS*/
const contacts = ref<EmergencyContactRequest[]>([
  {name: '', phoneNumber: '', relationship: '', primary: false}
]);

const hasPrimaryContact = computed(() => contacts.value.some(c => c.primary));

const isLastContactFilled = computed(() => {
  const lastContact = contacts.value[contacts.value.length - 1];
  return lastContact && lastContact.name?.trim() !== "" && lastContact.phoneNumber?.trim() !== "" && lastContact.relationship?.trim() !== "";
});

const addContact = () => {
  contacts.value.push({name: '', phoneNumber: '', relationship: '', primary: false});
};

const removeContact = (index: number) => {
  if (contacts.value.length > 1) {
    contacts.value.splice(index, 1);
  }
};

const patientStore = usePatientStore();
const toast = useToast();

const address = ref<AddressRequest>({
  street: '',
  city: '',
  state: '',
  zipCode: '',
});

const previousAddress = ref<AddressRequest>({
  street: '',
  city: '',
  state: '',
  zipCode: '',
});
const socialSecurityNumber = ref('');
const preferredContactMethod = ref('');
const preferredContactName = ref('');

const fetchContactInfo = async () => {
  try {
    const profile = patientStore.patientProfile;
    if (!profile) return;

    emails.value = profile.emails && profile.emails.length > 0
      ? profile.emails.map(e => ({
        email: e.email || '',
        isPrimary: e.primary ?? false
      }))
      : [{ email: '', isPrimary: false }];

    phoneNumbers.value = profile.phones && profile.phones.length > 0
      ? profile.phones.map(p => ({
        number: p.number || '',
        type: p.type || '',
        isPrimary: p.primary ?? false
    }))
      : [{ number: '', type: '', isPrimary: false }];

    contacts.value = profile.emergencyContacts && profile.emergencyContacts.length > 0
      ? profile.emergencyContacts.map(c => ({
        name: c.name || '',
        phoneNumber: c.phoneNumber || '',
        relationship: c.relationship || '',
        primary: c.primary ?? false
      }))
      : [{ name: '', phoneNumber: '', relationship: '', primary: false }];

    address.value = profile.address || {};
    previousAddress.value = profile.previousAddress || {};
    socialSecurityNumber.value = profile.socialSecurityNumber || "";
    preferredContactMethod.value = profile.preferredContactMethod || "";
    preferredContactName.value = profile.preferredContactName || "";

  } catch (error) {
    console.error("Error fetching contact information:", error);
  }
};

const setContactInfo = async () => {
  // check if previous address has any filled fields
  const hasPreviousAddress = previousAddress.value.street?.trim() ||
                            previousAddress.value.city?.trim() ||
                            previousAddress.value.state?.trim() ||
                            previousAddress.value.zipCode?.trim();

  const data = {
    address: address.value,
    previousAddress: hasPreviousAddress ? previousAddress.value : null,
    socialSecurityNumber: socialSecurityNumber.value,
    preferredContactMethod: preferredContactMethod.value,
    preferredContactName: preferredContactName.value,
    emails: emails.value,
    phoneNumbers: phoneNumbers.value,
    emergencyContacts: contacts.value
  };

  try {
    await api.patients.patientSetContactInformation(data);
    await patientStore.getPatientProfile();
    toast.success("Contact information updated successfully");
  } catch (error) {
    console.error("Error updating contact information:", error);
    toast.error("Failed to update contact information");
  }
};

watchEffect(() => {
  fetchContactInfo();
});

</script>
