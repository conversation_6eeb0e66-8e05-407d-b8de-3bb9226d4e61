<template>
  <div class="organization-page">
    <div class="flex items-center my-3 w-full px-4 py-4 sm:px-6 lg:px-8 lg:py-4">
      <h2 class="font-semibold text-4xl leading-10 text-grey-800">Bundles</h2>
    </div>
    <div class="px-4 sm:px-6 lg:px-8 mx-auto">
      <div class="flex">
        <div class="w-1/2 p-2">
          <InputText id="templateName" label="Title" />
          <!-- <Select id="locationId" label="Location" /> -->
          <Select id="priority" label="Priority" :options="priorities" />
          <Divider type="solid" />
          <FloatLabel variant="on" class="w-full">
            <AutoComplete
              v-model="selectedOrderEntry"
              :suggestions="orderEntryOptions"
              @complete="searchOrderEntries"
              optionLabel="displayName"
              label="Search patients..."
              input-id="selectedPatient"
              fluid
              class="w-full"
            />
            <label for="selectedPatient">Order Entry</label>
          </FloatLabel>
          <div class="px-4 sm:px-2 mx-auto">
            <EditMedicationOrderEntry
              v-if="isMedicine"
              :selected-order-entry="selectedOrderEntry"
              :editingOrderTemplateEntry="editingOrderTemplateEntry"
              :numberOfSteps="steps.length ?? 1"
              @close="removeOrderEntry"
              @submitMedicationOrderEntry="submitMedicationOrderEntry"
            />
            <EditLabOrderEntry
              v-if="isLab"
              :selected-order-entry="selectedOrderEntry"
              :editingOrderTemplateEntry="editingOrderTemplateEntry"
              :numberOfSteps="steps.length ?? 1"
              @close="removeOrderEntry"
              @submitLabOrderEntry="submitLabOrderEntry"
            />
            <EditProcedureOrderEntry
              v-if="isProcedure"
              :selected-order-entry="selectedOrderEntry"
              :editingOrderTemplateEntry="editingOrderTemplateEntry"
              :numberOfSteps="steps.length ?? 1"
              @close="removeOrderEntry"
              @submitLabOrderEntry="submitProcedureOrderEntry"
            />
          </div>
        </div>
        <div class="w-1/2 p-2">
          <div v-for="step in steps" :key="step.stepNumber">
            <div class="flex items-center justify-between">
              <h2 class="font-semibold text-2xl text-grey-800">Step {{ step.stepNumber }}</h2>
              <Button
                v-if="step.stepNumber == 1"
                label="New step"
                icon="pi pi-plus"
                @click="newStep"
                class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-primary text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none"
              />
              <Button
                v-if="
                  step.stepNumber != 1 &&
                  step.stepNumber == steps.length &&
                  step.orderEntries?.length == 0
                "
                label="Remove step"
                @click="removeStep"
                class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-red-600 text-white hover:!bg-red-700 focus:outline-none focus:!bg-red-700 disabled:opacity-50 disabled:pointer-events-none"
              />
            </div>
            <table class="min-w-full divide-y divide-gray-200">
              <tbody class="divide-y divide-gray-200">
                <template v-for="(row, index) in step.orderEntries" :key="index">
                  <tr class="relative hover:bg-gray-100 cursor-pointer">
                    <td class="h-px max-w-xs overflow-hidden whitespace-nowrap text-ellipsis">
                      <div class="flex px-6 py-3">
                        <span class="block text-sm text-gray-800 truncate" v-tooltip="row.name"
                          >{{ row?.name
                          }}<span class="text-red-500" v-if="row.isRequired">*</span></span
                        >
                        <button
                          v-if="row.orderType == 'Bundle'"
                          class="ml-2 focus:outline-none transition-transform"
                          @click.stop="toggleRow(index)"
                        >
                          <ChevronDownIcon
                            class="w-5 h-5 text-gray-500 transform transition-transform duration-300"
                            :class="{ 'rotate-180': expandedRows.has(index) }"
                          />
                        </button>
                      </div>
                    </td>
                    <td class="h-px w-72 whitespace-nowrap px-6 py-1.5">
                      <div v-if="row.orderType != 'Bundle'" class="flex items-right gap-x-4">
                        <!-- Edit Button -->
                        <a
                          class="inline-flex items-center gap-x-1 text-sm text-primary font-medium hover:underline focus:outline-none focus:underline"
                          href="#"
                          @click.prevent="editOrderEntry(row, step.stepNumber)"
                        >
                          <PencilSquareIcon class="w-4 h-4" />
                          Edit
                        </a>
                        <!-- Deactivate Button -->
                        <a
                          class="inline-flex items-center gap-x-1 text-sm text-red-500 font-medium hover:underline focus:outline-none focus:underline"
                          href="#"
                          @click.prevent="deleteOrderEntry(row.id!, step.stepNumber)"
                        >
                          <TrashIcon class="w-4 h-4" />

                          Delete
                        </a>
                      </div>
                    </td>
                  </tr>
                  <template v-if="row.orderType == 'Bundle' && expandedRows.has(index)">
                    <tr
                      v-for="entry in row.nestedOrderEntries"
                      :key="entry.id"
                      class="relative hover:bg-gray-100 cursor-pointer"
                    >
                      <td class="h-px max-w-xs overflow-hidden whitespace-nowrap text-ellipsis">
                        <div class="px-6 py-3">
                          <span
                            class="block text-sm text-gray-800 truncate pl-4"
                            v-tooltip="entry.name"
                            >{{ entry?.name
                            }}<span class="text-red-500" v-if="entry.isRequired">*</span></span
                          >
                        </div>
                      </td>
                      <td class="h-px w-72 whitespace-nowrap px-6 py-1.5">
                        <div class="flex items-right gap-x-4">
                          <!-- Edit Button -->
                          <a
                            class="inline-flex items-center gap-x-1 text-sm text-primary font-medium hover:underline focus:outline-none focus:underline"
                            href="#"
                            @click.prevent="editOrderEntry(entry, step.stepNumber, row.id)"
                          >
                            <PencilSquareIcon class="w-4 h-4" />
                            Edit
                          </a>
                          <!-- Deactivate Button -->
                          <a
                            class="inline-flex items-center gap-x-1 text-sm text-red-500 font-medium hover:underline focus:outline-none focus:underline"
                            href="#"
                            @click.prevent="deleteOrderEntry(entry.id!, step.stepNumber, row.id)"
                          >
                            <TrashIcon class="w-4 h-4" />

                            Delete
                          </a>
                        </div>
                      </td>
                    </tr>
                  </template>
                </template>
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <div class="flex justify-end">
        <button
          type="button"
          class="text-gray-500 px-4 py-2 mr-2 border-2 rounded-lg"
          @click="router.push({ name: 'order-bundle-templates' })"
        >
          Cancel
        </button>
        <Button
          label="Finish"
          @click="finish"
          class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-primary text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none"
        />
      </div>
    </div>
    <!-- </div> -->
    <!-- </div>
    </div> -->
  </div>
</template>
<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue'
import { api } from '@/api'
import FloatLabel from 'primevue/floatlabel'
import AutoComplete, { type AutoCompleteCompleteEvent } from 'primevue/autocomplete'
import type {
  OrderBundleTemplateStepResponse,
  OrderTemplateEntryResponse,
  SearchOrderEntryResponse,
} from '@/api/api-reference'
import debounce from 'lodash.debounce'
import EditMedicationOrderEntry from './EditMedicationOrderEntry.vue'
import EditLabOrderEntry from './EditLabOrderEntry.vue'
import EditProcedureOrderEntry from './EditProcedureOrderEntry.vue'
import Button from 'primevue/button'
import { PencilSquareIcon, TrashIcon } from '@heroicons/vue/24/outline'
import InputText from '../../components/form-extensions/InputTextFluent.vue'
import Select from '../../components/form-extensions/SelectFluent.vue'
import Divider from 'primevue/divider'
import * as yup from 'yup'
import { useForm } from 'vee-validate'
import { useAuthStore } from '@/stores/auth'
import { getPriorities } from '../../utils/priority'
import { useRoute } from 'vue-router'
import router from '../../router'
import { ChevronDownIcon } from '@heroicons/vue/24/outline'

const route = useRoute()
const authStore = useAuthStore()

const selectedOrderEntry = ref<SearchOrderEntryResponse>()
const editingOrderTemplateEntry = ref<OrderTemplateEntryResponse>()
const editingOrderStep = ref<number>()
const editingBundleId = ref<string>()
const orderEntryOptions = ref<SearchOrderEntryResponse[]>([])
const selectedRows = ref<string[]>([])
const priorities = getPriorities()
const steps = ref<OrderBundleTemplateStepResponse[]>([])

const isMedicine = computed(() => selectedOrderEntry.value?.type === 'Med')
const isLab = computed(() => selectedOrderEntry.value?.type === 'Lab')
const isProcedure = computed(() => selectedOrderEntry.value?.type === 'Procedure')

const expandedRows = ref(new Set<number>())
function toggleRow(index: number) {
  if (expandedRows.value.has(index)) {
    expandedRows.value.delete(index) // close this one
  } else {
    expandedRows.value.add(index) // open this one
  }
}

const initialValues = {
  templateName: '',
  locationId: authStore.user!.locationId!,
  priority: 'Routine',
}

const schema = yup.object({
  templateName: yup.string().required('Title is required'),
  locationId: yup.string().required('Location is required'),
  priority: yup.string().required('Priority is required'),
})

const { handleSubmit, setValues } = useForm({
  validationSchema: schema,
  initialValues: initialValues,
})

const submitOrderTemplate = handleSubmit(async (values) => {
  try {
    if (!route.params.id) {
      const response =
        await api.bundleTemplates.orderBundleTemplateCreateOrderBundleTemplate(values)
      router.push({
        name: 'edit-order-template',
        params: { id: response.data },
      })
    } else {
      await api.bundleTemplates.orderBundleTemplateEditOrderBundleTemplate({
        ...values,
        orderBundleTemplateId: route.params.id as string,
      })
    }
    return true
  } catch (error) {
    console.log(error)
    return false
  }
})

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const submitMedicationOrderEntry = async (medicationValues: any, stepNumber: number) => {
  await submitOrderTemplate()
  if (route.params.id) {
    if (editingOrderTemplateEntry.value) {
      await api.bundleTemplates.orderBundleTemplateEditMedicationOrderEntryInBundleTemplate({
        ...medicationValues,
        orderBundleTemplateId: route.params.id as string,
        orderEntryId: medicationValues.id,
        stepNumber: editingOrderStep.value,
        ingredientId: selectedOrderEntry.value!.id!,
        nestedBundleId: editingBundleId.value,
      })
    } else {
      await api.bundleTemplates.orderBundleTemplateAddMedicationOrderEntryToBundleTemplate({
        ...medicationValues,
        orderBundleTemplateId: route.params.id as string,
        stepNumber: stepNumber,
        ingredientId: selectedOrderEntry.value!.id!,
      })
    }

    selectedOrderEntry.value = undefined
    editingOrderStep.value = undefined
    editingBundleId.value = undefined
    editingOrderTemplateEntry.value = undefined
    const response = await api.bundleTemplates.orderBundleTemplateGetOrderBundleTemplate(
      route.params.id as string,
    )
    const data = response.data
    steps.value = data.steps ?? []
  }
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const submitLabOrderEntry = async (labValues: any, stepNumber: number) => {
  await submitOrderTemplate()
  if (route.params.id) {
    if (editingOrderTemplateEntry.value) {
      await api.bundleTemplates.orderBundleTemplateEditLabOrderEntryInBundleTemplate({
        ...labValues,
        orderBundleTemplateId: route.params.id as string,
        orderEntryId: labValues.id,
        stepNumber: editingOrderStep.value,
        loincCodeId: selectedOrderEntry.value!.id!,
        nestedBundleId: editingBundleId.value,
      })
    } else {
      await api.bundleTemplates.orderBundleTemplateAddLabOrderEntryToBundleTemplate({
        ...labValues,
        orderBundleTemplateId: route.params.id as string,
        stepNumber: stepNumber,
        loincCodeId: selectedOrderEntry.value!.id!,
      })
    }

    selectedOrderEntry.value = undefined
    editingOrderStep.value = undefined
    editingBundleId.value = undefined
    editingOrderTemplateEntry.value = undefined
    const response = await api.bundleTemplates.orderBundleTemplateGetOrderBundleTemplate(
      route.params.id as string,
    )
    const data = response.data
    steps.value = data.steps ?? []
  }
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const submitProcedureOrderEntry = async (procedureValues: any, stepNumber: number) => {
  await submitOrderTemplate()
  if (route.params.id) {
    if (editingOrderTemplateEntry.value) {
      await api.bundleTemplates.orderBundleTemplateEditProcedureOrderEntryInBundleTemplate({
        ...procedureValues,
        orderBundleTemplateId: route.params.id as string,
        orderEntryId: procedureValues.id,
        stepNumber: editingOrderStep.value,
        snomedCodeId: selectedOrderEntry.value!.id!,
        nestedBundleId: editingBundleId.value,
      })
    } else {
      await api.bundleTemplates.orderBundleTemplateAddProcedureOrderEntryToBundleTemplate({
        ...procedureValues,
        orderBundleTemplateId: route.params.id as string,
        stepNumber: stepNumber,
        snomedCodeId: selectedOrderEntry.value!.id!,
      })
    }
    selectedOrderEntry.value = undefined
    editingOrderStep.value = undefined
    editingBundleId.value = undefined
    editingOrderTemplateEntry.value = undefined
    const response = await api.bundleTemplates.orderBundleTemplateGetOrderBundleTemplate(
      route.params.id as string,
    )
    const data = response.data
    steps.value = data.steps ?? []
  }
}

const newStep = async () => {
  await api.bundleTemplates.orderBundleTemplateAddOrderBundleTemplateStep({
    orderBundleTemplateId: route.params.id as string,
    stepNumber: steps.value.length + 1,
  })
  const response = await api.bundleTemplates.orderBundleTemplateGetOrderBundleTemplate(
    route.params.id as string,
  )
  const data = response.data
  steps.value = data.steps ?? []
}

const removeStep = async () => {
  await api.bundleTemplates.orderBundleTemplateDeleteOrderBundleTemplateStep({
    orderBundleTemplateId: route.params.id as string,
    stepNumber: steps.value.length,
  })
  const response = await api.bundleTemplates.orderBundleTemplateGetOrderBundleTemplate(
    route.params.id as string,
  )
  const data = response.data
  steps.value = data.steps ?? []
}

const finish = async () => {
  if (await submitOrderTemplate())
    router.push({
      name: 'order-bundle-templates',
    })
}

onMounted(async () => {
  if (route.params.id) {
    const response = await api.bundleTemplates.orderBundleTemplateGetOrderBundleTemplate(
      route.params.id as string,
    )
    const data = response.data
    steps.value = data.steps ?? []
    setValues({
      templateName: data.templateName,
      priority: data.priority,
    })
  }
})

const fetchOrderEntries = async (query: string) => {
  editingOrderTemplateEntry.value = undefined
  editingOrderStep.value = undefined
  editingBundleId.value = undefined
  if (!query.trim()) {
    return
  }
  const orderEntriesResponse = await api.bundleTemplates.orderBundleTemplateSearchOrderEntry({
    searchParam: query,
  })

  orderEntryOptions.value = orderEntriesResponse.data.items ?? []
}

const searchOrderEntries = debounce((event: AutoCompleteCompleteEvent) => {
  fetchOrderEntries(event.query)
}, 300)

const editOrderEntry = (
  row: OrderTemplateEntryResponse,
  step: number | undefined,
  bundleId: string | undefined = undefined,
) => {
  selectedOrderEntry.value = row.orderEntry
  editingOrderTemplateEntry.value = row
  editingOrderStep.value = step
  editingBundleId.value = bundleId
}

const deleteOrderEntry = async (
  rowId: string,
  stepNumber: number | undefined,
  bundleId: string | undefined = undefined,
) => {
  await api.bundleTemplates.orderBundleTemplateDeleteOrderEntryFromBundleTemplate({
    orderBundleTemplateId: route.params.id as string,
    stepNumber: stepNumber ?? 1,
    orderEntryId: rowId,
    nestedBundleId: bundleId,
  })
  const response = await api.bundleTemplates.orderBundleTemplateGetOrderBundleTemplate(
    route.params.id as string,
  )
  const data = response.data
  steps.value = data.steps ?? []
}

const removeOrderEntry = (id: string) => {
  selectedOrderEntry.value = undefined
  editingOrderTemplateEntry.value = undefined
  if (id) {
    selectedRows.value.push(id)
  }
}

watch(
  () => selectedOrderEntry.value,
  async (newValue) => {
    if (newValue?.type == 'Bundle') {
      await api.bundleTemplates.orderBundleTemplateAddBundleOrderEntryToBundleTemplate({
        orderBundleTemplateId: route.params.id as string,
        stepNumber: 1,
        bundleId: newValue.id,
      })
      const response = await api.bundleTemplates.orderBundleTemplateGetOrderBundleTemplate(
        route.params.id as string,
      )
      const data = response.data
      steps.value = data.steps ?? []
    }
  },
)
</script>
