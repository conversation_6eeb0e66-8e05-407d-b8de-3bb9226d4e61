<template>
  <div class="flex bg-gray-100 px-4 py-2 shadow-md items-center gap-2 h-12">
    <h1 class="text-sm font-bold">
      <Select
        v-model="encounterStore.selectedEncounter"
        :options="encounterStore.activeEncounters"
        option-label="fullName"
      />
    </h1>
    <p
      v-if="encounterStore.selectedEncounter?.patientBirthday && !encounterStore.isMobile"
      class="text-sm"
    >
      <span class="font-bold">AGE: </span>{{ patientAge }} |
    </p>
    <p v-if="encounterStore.selectedEncounter?.patientBirthday" class="text-sm">
      <span class="font-bold">DOB: </span
      >{{ formatDateWithoutTime(encounterStore.selectedEncounter?.patientBirthday) }} |
    </p>
    <p
      v-if="encounterStore.selectedEncounter?.patientBirthday && !encounterStore.isMobile"
      class="text-sm"
    >
      <span class="font-bold">Birth Sex: </span>Female |
    </p>
    <p
      v-if="encounterStore.selectedEncounter?.phoneNumber && !encounterStore.isMobile"
      class="text-sm"
    >
      <span class="font-bold">Phone: </span>{{ encounterStore.selectedEncounter?.phoneNumber }}
    </p>
    <Button variant="link" class="ml-auto" @click="encounterStore.reset()"
      ><ArrowPathIcon class="h-5 w-5"
    /></Button>
  </div>
  <!-- Visible buttons on large screens -->
  <div class="hidden justify-between items-center xl:flex gap-2 flex-wrap h-12">
    <Button
      variant="link"
      label="Active Patients"
      @click="encounterStore.setActiveBox('active-patients')"
    />
    <Button variant="link" label="En. History" @click="encounterStore.setActiveBox('history')" />
    <Button variant="link" label="Care" />
    <Button
      variant="link"
      label="Questionnaires"
      @click="encounterStore.setActiveBox('questionnaires')"
    />
    <Button variant="link" label="Vitals" @click="encounterStore.setActiveBox('vitals')" />
    <Button variant="link" label="Orders" @click="encounterStore.setActiveBox('orders')" />
    <Button variant="link" label="Notes" @click="encounterStore.setActiveBox('notes')" />
    <Button variant="link" label="Labs" @click="encounterStore.setActiveBox('labs')" />
    <Button variant="link" label="Imaging" @click="encounterStore.setActiveBox('imaging')" />
    <Button variant="link" label="Comms" @click="encounterStore.setActiveBox('comms')" />
    <Button variant="link" label="Scratch" @click="encounterStore.setActiveBox('scratch')" />
    <Button variant="link" label="S" @click="encounterStore.setActiveBox('summary')" />
  </div>

  <!-- Dropdown for smaller screens -->
  <div class="flex block xl:hidden">
    <Button
      icon="pi pi-bars"
      @click="toggleMenu"
      severity="secondary"
      aria-label="Menu"
      class="ml-auto"
    />
    <Menu ref="menuRef" :model="menuOptions" :popup="true" />
  </div>
  <div class="flex">
    <!-- Main Content -->
    <main class="flex-1 bg-gray-50 relative">
      <div class="parent" style="height: calc(100vh - 200px)">
        <ActivePatients />
        <EncountersHistory />
        <Scratch />
        <VitalSignBox />
        <Questionnaires />
        <LabBox />
        <ImagingResultBox />
        <CommsTable />
        <OrderTable />
        <Notes />
        <Summary />
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onBeforeMount, watch, onMounted, onBeforeUnmount } from 'vue'
import 'vue-draggable-resizable/style.css'
import ActivePatients from './ActivePatients.vue'
import EncountersHistory from './EncounterHistory.vue'
import Button from 'primevue/button'
import Scratch from './Scratch.vue'
import VitalSignBox from './VitalSigns/VitalSignBox.vue'
import Questionnaires from './Questionnaires.vue'
import Summary from './Summary.vue'
import { formatDateWithoutTime } from '@/utils/timeMethods'
import Select from 'primevue/select'
import { useEncounterStore } from '@/stores/encounter.ts'
import Menu from 'primevue/menu'
import { ArrowPathIcon } from '@heroicons/vue/24/outline'
import LabBox from './Labs/LabBox.vue'
import ImagingResultBox from './ImagingResultBox.vue'
import CommsTable from './Communication/CommsTable.vue'
import OrderTable from './Order/OrderTable.vue'
import Notes from '@/pages/Encounter/Notes.vue'
import { useRoute, useRouter } from 'vue-router'

const encounterStore = useEncounterStore()
const route = useRoute()
const router = useRouter()

onBeforeMount(async () => {
  encounterStore.setEncounterBoxes()
  await encounterStore.getActiveEncounters()
  if (route.params.id) {
    encounterStore.setSelectedEncounterById(route.params.id as string)
  } else {
    encounterStore.setSelectedEncounterDefault(router, route)
  }
})

const patientAge = computed(() => {
  if (!encounterStore.selectedEncounter) return 'n/a'
  const birthDate = new Date(encounterStore.selectedEncounter.patientBirthday!)
  const today = new Date()

  let age = today.getFullYear() - birthDate.getFullYear()
  const monthDifference = today.getMonth() - birthDate.getMonth()

  // If the birthday hasn't occurred yet this year, subtract one year
  if (monthDifference < 0 || (monthDifference === 0 && today.getDate() < birthDate.getDate())) {
    age--
  }

  return age
})

watch(
  () => encounterStore.selectedEncounter,
  async (newEncounter) => {
    await encounterStore.getEncounter(newEncounter!)
    router.replace({ name: route.name!, params: { id: newEncounter?.id } })
  },
)

const menuRef = ref()

const toggleMenu = (event: MouseEvent) => {
  menuRef.value?.toggle(event)
}
const menuOptions = [
  { label: 'En. History', command: () => encounterStore.setActiveBox('history') },
  { label: 'Care', command: () => {} },
  { label: 'Questionnaires', command: () => encounterStore.setActiveBox('questionnaires') },
  { label: 'Vitals', command: () => encounterStore.setActiveBox('vitals') },
  { label: 'Orders', command: () => encounterStore.toggleIsBoxOpen('orders') },
  { label: 'Notes', command: () => encounterStore.toggleIsBoxOpen('notes') },
  { label: 'Labs', command: () => encounterStore.toggleIsBoxOpen('labs') },
  { label: 'Imaging', command: () => encounterStore.toggleIsBoxOpen('imaging') },
  { label: 'Comms', command: () => encounterStore.setActiveBox('comms') },
  { label: 'Scratch', command: () => encounterStore.setActiveBox('scratch') },
  { label: 'S', command: () => encounterStore.setActiveBox('summary') },
]

const handleResize = () => {
  console.log('Resized to', window.innerWidth, window.innerHeight)
  // Recalculate boxes
  if (window.innerWidth > 250 && window.innerHeight > 450) {
    encounterStore.updateBoxDimensions(window.innerWidth, window.innerHeight)
  }
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
})
</script>
<style scoped>
.draggable {
  outline: none !important;
  border: 2px solid blue !important;
}
</style>
