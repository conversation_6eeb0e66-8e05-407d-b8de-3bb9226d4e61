<template>
  <div v-if="isVisible" class="p-3 rounded space-y-3">
    <InputTextFluent id="subject" label="Subject" />

    <div class="flex items-center space-x-2">
      <FileUpload
        mode="basic"
        chooseLabel="Create link"
        chooseIcon="pi pi-link"
        custom-upload
        auto
        @select="handleFileSelect($event)"
      />
    </div>
    <Textarea id="message" label="Message" v-model="messageText" rows="10" cols="20" />

    <Button
      label="Send"
      @click="sendEmail"
      class="w-full font-medium rounded-lg border border-transparent bg-primary text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none"
    />
  </div>
</template>
<script setup lang="ts">
import type { FileUploadSelectEvent } from 'primevue/fileupload'
import { useForm } from 'vee-validate'
import { ref, watch } from 'vue'
import { useEncounterStore } from '@/stores/encounter.ts'
import { api } from '@/api'
import InputTextFluent from '@/components/form-extensions/InputTextFluent.vue'
import Textarea from '@/components/form-extensions/TextareaFluent.vue'
import FileUpload from 'primevue/fileupload'
import Button from 'primevue/button'

const encounterStore = useEncounterStore()

defineProps<{ isVisible: boolean }>()
const emit = defineEmits(['close'])
const selectedFileUrls = ref<string[]>([])
const messageText = ref('')

const { handleSubmit, resetForm } = useForm()

const handleFileSelect = async (event: FileUploadSelectEvent) => {
  if (event.files && event.files[0]) {
    console.log(event.files[0])
    const response = await api.encounter.encounterUploadFileForEncounterCommunication(
      encounterStore.selectedEncounter!.id!,
      {
        EncounterId: encounterStore.selectedEncounter!.id!,
        PatientId: encounterStore.selectedEncounter!.patientId!,
        Document: event.files[0],
      },
    )
    selectedFileUrls.value.push(response.data)
    messageText.value += response.data
  }
}

const sendEmail = handleSubmit(async (values) => {
  try {
    const data = {
      ...values,
      MessageType: 'Sms',
      PatientId: encounterStore.selectedEncounter!.patientId!,
      EncounterId: encounterStore.selectedEncounter?.id,
    }
    await api.encounter.encounterSendMessageToPatient(encounterStore.selectedEncounter!.id!, data)
    resetForm()
    selectedFileUrls.value = []
    await api.encounter.encounterGetCommunications(encounterStore.selectedEncounter!.id!)
    emit('close')
  } catch (error) {
    console.log(error)
  }
})

watch(
  () => encounterStore.selectedEncounter,
  () => {
    resetForm()
    selectedFileUrls.value = []
    emit('close')
  },
)
</script>
