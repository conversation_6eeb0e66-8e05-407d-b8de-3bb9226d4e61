<template>
  <ResizableBox title="VITALS" type="vitals">
    <div class="px-4 sm:px-2 mx-auto">
      <div class="flex flex-col">
        <div class="-m-1.5 overflow-x-auto">
          <div class="p-1.5 min-w-full inline-block align-middle">
            <div class="bg-white border border-gray-200 rounded-xl shadow-sm overflow-hidden">
              <TableHeader>
                <template #inputs>
                  <div class="relative max-w-sm">
                    <MultiSelect
                      v-model="selectedMeasurements"
                      :options="measurements"
                      optionLabel="textLong"
                      placeholder="Select Columns"
                      :maxSelectedLabels="3"
                      class="w-full border border-gray-200 shadow-sm rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none"
                    />
                  </div>
                </template>

                <template #buttons>
                  <a
                    class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-primary text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none"
                    href="#"
                    @click="recordVitalsOpen = true"
                  >
                    <PlusIcon class="shrink-0 w-4 h-4" />
                    Record Vitals
                  </a>
                </template>
              </TableHeader>
              <!-- Table -->
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th
                      v-for="measurement in selectedMeasurements"
                      :key="measurement.type"
                      scope="col"
                      class="ps-6 py-3 text-start"
                    >
                      <div class="flex items-center gap-x-2">
                        <span class="text-xs font-semibold tracking-wide text-gray-800">
                          {{ formatColumnName(measurement) }}
                        </span>
                      </div>
                    </th>

                    <th scope="col" class="px-6 py-3 text-end">
                      <div class="flex items-center gap-x-2">
                        <span class="text-xs font-semibold tracking-wide text-gray-800">
                          Date
                        </span>
                      </div>
                    </th>
                  </tr>
                </thead>

                <tbody class="divide-y divide-gray-200">
                  <tr v-for="vitalSign in filteredVitalSigns" :key="vitalSign.date">
                    <td
                      v-for="measurement in selectedMeasurements"
                      :key="measurement.type"
                      class="h-px w-72 whitespace-nowrap"
                    >
                      <div v-if="measurement.valueType == 'bloodPressure'" class="px-6 py-3">
                        <span
                          v-if="
                            vitalSign.measurements?.filter(
                              (x) => x.type == 'SystolicBloodPressure',
                            )[0]?.value
                          "
                          class="block text-sm text-gray-800"
                          >{{
                            vitalSign.measurements?.filter(
                              (x) => x.type == 'SystolicBloodPressure',
                            )[0]?.value
                          }}/{{
                            vitalSign.measurements?.filter(
                              (x) => x.type == 'DiastolicBloodPressure',
                            )[0]?.value
                          }}</span
                        >
                      </div>
                      <div v-else class="px-6 py-3">
                        <span class="block text-sm text-gray-800">{{
                          vitalSign.measurements?.filter((x) => x.type == measurement.type)[0]
                            ?.value
                        }}</span>
                      </div>
                    </td>
                    <td class="size-px whitespace-nowrap">
                      <div class="px-6 py-1.5">
                        <span class="block text-sm text-gray-800">{{
                          formatDate(vitalSign.date)
                        }}</span>
                      </div>
                    </td>
                  </tr>
                  <tr v-if="recordVitalsOpen">
                    <td
                      v-for="measurement in selectedMeasurements"
                      :key="measurement.type"
                      class="h-px w-72 whitespace-nowrap"
                    >
                      <div v-if="measurement.valueType == 'number'" class="px-2">
                        <InputNumber
                          :id="measurement.type"
                          label=""
                          mode="decimal"
                          :minFractionDigits="0"
                          :maxFractionDigits="5"
                        />
                      </div>
                      <div v-if="measurement.valueType == 'bloodPressure'" class="px-2">
                        <InputMask id="BloodPressure" label="" />
                      </div>
                    </td>
                    <td class="size-px whitespace-nowrap">
                      <div class="flex px-4 py-1">
                        <button
                          type="button"
                          class="text-gray-500 px-2 py-2 mr-2"
                          @click="recordVitalsOpen = false"
                        >
                          Cancel
                        </button>
                        <button
                          @click="recordVitals"
                          class="bg-primary text-white px-4 py-2 rounded"
                        >
                          Save
                        </button>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
              <!-- End Table -->
            </div>
          </div>
        </div>
      </div>
    </div>
    <VitalsChart :selectedMeasurement="selectedMeasurements" />
  </ResizableBox>
</template>
<script setup lang="ts">
import ResizableBox from '../../../components/resizable/ResizableBox.vue'
import { computed, ref } from 'vue'
import { useEncounterStore } from '@/stores/encounter.ts'
import { api } from '@/api'
import TableHeader from '../../../components/table/TableHeader.vue'
import InputNumber from '../../../components/form-extensions/InputNumberFluent.vue'
import { PlusIcon } from '@heroicons/vue/24/outline'
import { useForm } from 'vee-validate'
import type { VitalSignRequest } from '@/api/api-reference.ts'
import { formatDate } from '@/utils/timeMethods'
import InputMask from '@/components/form-extensions/InputMaskFluent.vue'
import VitalsChart from './VitalsChart.vue'
import type { Measurement } from '@/utils/interfaces.ts'
import MultiSelect from 'primevue/multiselect'
import { getDefaultMeasurements } from '@/utils/vitalSigns.ts'

const measurements = ref<Measurement[]>(getDefaultMeasurements())

const encounterStore = useEncounterStore()
const selectedMeasurements = computed({
  get: () => measurements.value.filter((m) => m.selected),
  set: (selected) => {
    measurements.value.forEach((m) => {
      m.selected = selected.includes(m)
    })
  },
})

const formatColumnName = (measurement: Measurement) => {
  let columnName = measurement.textShort
  if (measurement.unit) {
    columnName += ' (' + measurement.unit + ')'
  }
  return columnName
}

const recordVitalsOpen = ref(false)

const vitalSigns = computed(() => encounterStore.vitalSigns)
const filteredVitalSigns = computed(() => {
  const selectedTypes = new Set(
    selectedMeasurements.value.map((m) =>
      m.type === 'BloodPressure' ? 'SystolicBloodPressure' : m.type,
    ),
  )

  return (
    vitalSigns.value?.filter((vitalSign) =>
      vitalSign.measurements?.some((measurement) => selectedTypes.has(measurement.type!)),
    ) ?? []
  )
})

const { handleSubmit } = useForm()

const recordVitals = handleSubmit(async (values) => {
  const vitals: VitalSignRequest[] = Object.entries(values).flatMap(([key, value]) => {
    if (key === 'BloodPressure' && typeof value === 'string') {
      const [systolic, diastolic] = value.split('/').map((v) => v.trim())

      return [
        { type: 'SystolicBloodPressure', value: systolic || undefined },
        { type: 'DiastolicBloodPressure', value: diastolic || undefined },
      ].filter((v) => v.value !== undefined)
    }

    return value ? [{ type: key, value: String(value) }] : []
  })

  try {
    await api.encounter.encounterAddVitalSigns({
      vitals: vitals,
      patientId: encounterStore.selectedEncounter!.patientId!,
      encounterId: encounterStore.selectedEncounter?.id,
    })
    await encounterStore.getPatientVitalSigns(encounterStore.selectedEncounter!.patientId!)
    recordVitalsOpen.value = false
  } catch (error) {
    console.log(error)
  }
})
</script>
