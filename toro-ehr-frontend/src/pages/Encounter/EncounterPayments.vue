<template>
  <div class="max-w-5xl mx-auto p-6 space-y-8">
    <!-- Operation Result Message -->
    <div v-if="paymentResult" class="p-4 rounded-lg border-l-4 shadow-sm"
         :class="paymentResult.success
           ? 'bg-green-50 border-green-400 text-green-800'
           : 'bg-red-50 border-red-400 text-red-800'">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <i :class="paymentResult.success ? 'pi pi-check-circle text-green-400' : 'pi pi-times-circle text-red-400'"
             class="text-lg"></i>
        </div>
        <div class="ml-3">
          <div class="font-semibold">
            {{ paymentResult.success ? 'Operation Successful' : 'Operation Failed' }}
          </div>
          <div class="text-sm mt-1">{{ paymentResult.message }}</div>
        </div>
        <div class="ml-auto">
          <Button
            icon="pi pi-times"
            variant="text"
            size="small"
            @click="paymentResult = null"
            class="text-gray-400 hover:text-gray-600"
          />
        </div>
      </div>
    </div>

    <!-- Payment Form -->
    <PaymentForm
      v-if="patientId"
      :encounterId="encounterId"
      :patientId="patientId"
      @paymentSuccess="handlePaymentSuccess"
      @paymentError="handlePaymentError"
    />

    <!-- Loading state for payment form -->
    <div v-else class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <h1 class="text-xl font-semibold text-gray-900">New Payment</h1>
      </div>
      <div class="p-6 flex items-center justify-center">
        <div class="flex items-center space-x-2 text-gray-500">
          <i class="pi pi-spin pi-spinner"></i>
          <span>Loading payment options...</span>
        </div>
      </div>
    </div>

    <!-- Transaction History -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <h2 class="text-lg font-semibold text-gray-900">Transaction History</h2>
      </div>

      <!-- Desktop Table -->
      <div class="hidden md:block overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Method</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Card</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="tx in transactions" :key="tx.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ formatDate(tx.date) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                ${{ tx.amount?.toFixed(2) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ formatTransactionType(tx.transactionType) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ tx.paymentMethod === 'PosTerminal' ? 'Terminal' :
                   tx.paymentMethod === 'SavedCard' ? 'Card On File' : 'N/A' }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ tx.cardType ?? 'Card' }} •••• {{ tx.last4 ?? '----' }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                      :class="getStatusClasses(tx.status)">
                  <span class="w-1.5 h-1.5 rounded-full mr-1.5" :class="getStatusDotClass(tx.status)"></span>
                  {{ tx.status }}
                </span>
              </td>
              <td class="h-px w-72 whitespace-nowrap px-6 py-1.5">
                <Button
                  icon="pi pi-ellipsis-v"
                  variant="text"
                  rounded
                  @click="toggle($event, tx.id!)"
                  :disabled="tx.status?.toLowerCase() !== 'approved'"
                  :class="{ 'opacity-50 cursor-not-allowed': tx.status?.toLowerCase() !== 'approved' }"
                  v-tooltip.top="tx.status?.toLowerCase() !== 'approved' ? 'Only approved transactions can be refunded' : 'Refund options'"
                  aria-haspopup="true"
                  aria-controls="overlay_menu"
                />
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Mobile Cards -->
      <div class="md:hidden divide-y divide-gray-200">
        <div v-for="tx in transactions" :key="tx.id" class="p-4 space-y-3">
          <div class="flex justify-between items-start">
            <div>
              <div class="text-sm font-medium text-gray-900">${{ tx.amount?.toFixed(2) }}</div>
              <div class="text-xs text-gray-500">{{ formatDate(tx.date) }}</div>
            </div>
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  :class="getStatusClasses(tx.status)">
              <span class="w-1.5 h-1.5 rounded-full mr-1.5" :class="getStatusDotClass(tx.status)"></span>
              {{ tx.status }}
            </span>
          </div>
          <div class="text-sm text-gray-600">
            <div>{{ formatTransactionType(tx.transactionType) }} •
                 {{ tx.paymentMethod === 'PosTerminal' ? 'Terminal' :
                    tx.paymentMethod === 'SavedCard' ? 'Card On File' : 'N/A' }}</div>
            <div>{{ tx.cardType ?? 'Card' }} •••• {{ tx.last4 ?? '----' }}</div>
          </div>
          <div class="flex justify-between items-center">
            <Button
              variant="link"
              label="Refund"
              rounded
              @click="toggle($event, tx.id!)"
              :disabled="tx.status?.toLowerCase() !== 'approved'"
              :class="{ 'opacity-50 cursor-not-allowed': tx.status?.toLowerCase() !== 'approved' }"
              v-tooltip.top="tx.status?.toLowerCase() !== 'approved' ? 'Only approved transactions can be refunded' : 'Refund options'"
              aria-haspopup="true"
              aria-controls="overlay_menu"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <Menu ref="menu" id="overlay_menu" :model="items" :popup="true"/>

  <!-- Refund Modal -->
  <RefundModal
    :isOpen="isRefundModalOpen"
    :transaction="selectedTransactionData"
    :refundType="refundType"
    :isLoading="isRefundLoading"
    @close="closeRefundModal"
    @refund="handleRefund"
  />
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import { api } from '@/api'
import Button from 'primevue/button'
import Menu from 'primevue/menu'
import type { EncounterTransactionResponse, ProcessPaymentResponse } from '@/api/api-reference'
import { formatDate } from '@/utils/timeMethods'
import PaymentForm from '@/components/PaymentForm.vue'
import RefundModal from './RefundModal.vue'

const route = useRoute()
const encounterId = route.params.encounterId as string

const patientId = ref<string>('')
const transactions = ref<EncounterTransactionResponse[]>([])
const paymentResult = ref<ProcessPaymentResponse | null>(null)

const menu = ref()
const selectedTransaction = ref()
const isRefundModalOpen = ref(false)
const refundType = ref<'total' | 'partial'>('total')
const selectedTransactionData = ref<EncounterTransactionResponse | null>(null)
const isRefundLoading = ref(false)

const items = ref([
  {
    label: 'Refund total',
    icon: 'pi pi-dollar',
    command: () => openRefundModal('total')
  },
  {
    label: 'Refund partially',
    icon: 'pi pi-dollar',
    command: () => openRefundModal('partial')
  }
])

watch(paymentResult, (newResult) => {
  if (newResult?.success) {
    setTimeout(() => {
      paymentResult.value = null
    }, 5000)
  }
})

const handlePaymentSuccess = async (result: ProcessPaymentResponse) => {
  paymentResult.value = result
  await refreshTransactions()
}

const handlePaymentError = (error: string) => {
  paymentResult.value = { success: false, message: error }
}

const refreshTransactions = async () => {
  try {
    const response = await api.encounter.encounterListEncounterTransactions(encounterId)
    transactions.value = response.data
  } catch (error) {
    console.error('Failed to refresh transactions', error)
  }
}

const formatTransactionType = (type?: string): string => {
  switch (type) {
    case 'Charge': return 'Charge'
    case 'Refund': return 'Refund'
    case 'Void': return 'Void'
    default: return type ?? '—'
  }
}

const getStatusClasses = (status?: string): string => {
  switch (status?.toLowerCase()) {
    case 'approved': return 'bg-green-100 text-green-800'
    case 'pending': return 'bg-yellow-100 text-yellow-800'
    case 'declined': return 'bg-red-100 text-red-800'
    case 'voided': return 'bg-purple-100 text-purple-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const getStatusDotClass = (status?: string): string => {
  switch (status?.toLowerCase()) {
    case 'approved': return 'bg-green-400'
    case 'pending': return 'bg-yellow-400'
    case 'declined': return 'bg-red-400'
    case 'voided': return 'bg-purple-400'
    default: return 'bg-gray-400'
  }
}

const toggle = (event: MouseEvent, id: string) => {
  const transaction = transactions.value.find(tx => tx.id === id)
  if (transaction?.status?.toLowerCase() === 'approved') {
    menu.value.toggle(event)
    selectedTransaction.value = id
  }
}

const openRefundModal = (type: 'total' | 'partial') => {
  if (selectedTransaction.value) {
    const transaction = transactions.value.find(tx => tx.id === selectedTransaction.value)
    if (transaction) {
      selectedTransactionData.value = transaction
      refundType.value = type
      isRefundModalOpen.value = true
    }
  }
}

const closeRefundModal = () => {
  isRefundModalOpen.value = false
  selectedTransactionData.value = null
  isRefundLoading.value = false
}

const handleRefund = async (refundAmount: number) => {
  if (!selectedTransactionData.value) return

  isRefundLoading.value = true
  try {
    let response
    if (refundType.value === 'total') {
      response = await api.encounter.encounterVoidOrRefundTotal(selectedTransactionData.value.id!)
      paymentResult.value = Array.isArray(response.data) ? response.data[0] : response.data
    } else {
      response = await api.encounter.encounterCustomRefund(selectedTransactionData.value.id!, {
        transactionId: selectedTransactionData.value.id!,
        refundAmount: refundAmount
      })
      paymentResult.value = response.data
    }

    await refreshTransactions()
    closeRefundModal()
  } catch (error) {
    console.error('Refund failed', error)
    paymentResult.value = { success: false, message: 'Refund failed. Please try again.' }
  } finally {
    isRefundLoading.value = false
  }
}

onMounted(async () => {
  try {
    // Fetch encounter details to get patientId
    const encounterDetails = await api.encounter.encounterGetEncounterById(encounterId)
    patientId.value = encounterDetails.data.patientId || ''

    if (!patientId.value) {
      console.warn('No patientId found in encounter details for encounter:', encounterId)
    }

    await refreshTransactions()
  } catch (error) {
    console.error('Failed to fetch encounter details', error)
  }
})
</script>
