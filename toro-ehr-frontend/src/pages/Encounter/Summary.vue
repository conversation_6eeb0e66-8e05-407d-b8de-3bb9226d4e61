<template>
  <ResizableBox title="SUMMARY" type="summary" layout="right">
    <div class="space-y-4 p-4">
      <!-- Top Stats -->
      <div class="flex flex-col md:flex-row md:flex-wrap gap-2 text-sm text-gray-700">
        <div class="flex gap-1" v-if="lastHeight">
          <span class="font-semibold">HEIGHT:</span>
          {{ lastHeight?.cm }}cm | {{ lastHeight?.feet }}'{{ lastHeight?.inches }}"
        </div>
        <div class="flex gap-1" v-if="lastWeight">
          <span class="font-semibold">WEIGHT:</span>
          {{ lastWeight?.kg }}kg | {{ lastWeight?.lbs }}lbs
        </div>
        <div class="flex gap-1" v-if="bmi">
          <span class="font-semibold">BMI =</span>
          {{ bmi }}
        </div>
      </div>

      <div class="flex flex-col md:flex-row md:flex-wrap gap-2 text-sm text-gray-700">
        <div class="flex gap-1">
          <span class="font-semibold">Last encounter:</span>
          {{ formatDate(encounterStore.selectedEncounter?.previouesEncounterDate) }}
        </div>
        <div class="flex gap-1">
          <span class="font-semibold">Patient reason for visit:</span>
          stomach ache
        </div>
      </div>

      <!-- Accordion -->
      <Accordion :value="['0']" multiple>
        <!-- Latest Vitals -->
        <AccordionPanel value="0">
          <AccordionHeader>{{ latestVitalsDate }}</AccordionHeader>
          <AccordionContent>
            <ul class="text-sm text-gray-800 space-y-1">
              <li v-for="(item, index) in latestMeasurementsFormatted" :key="index">
                {{ item }}
              </li>
            </ul>
          </AccordionContent>
        </AccordionPanel>

        <!-- Problem List / Care -->
        <AccordionPanel value="1">
          <AccordionHeader>Problem List / Care</AccordionHeader>
          <AccordionContent>
            <ul class="text-sm text-gray-800 space-y-1">
              <li>Hypertension | active | confirmed | Diagnosed 2/12/2020</li>
              <li>Fatty Liver Disease | active | confirmed | Diagnosed 5/22/2022</li>
              <li>Type 2 Diabetes | active | confirmed | Diagnosed 2/12/2010</li>
            </ul>
            <div class="text-center text-gray-500 text-xs mt-2">show more</div>
          </AccordionContent>
        </AccordionPanel>

        <!-- Medications -->
        <AccordionPanel value="2">
          <AccordionHeader>Medications</AccordionHeader>
          <AccordionContent>
            <ul class="text-sm text-gray-800 space-y-1">
              <li v-for="(item, index) in encounterStore.patientProfile?.medications" :key="index">
                {{ item.displayName }}, patient specified
              </li>
              <li
                v-for="(item, index) in encounterStore.patientActiveMedicationOrders"
                :key="index"
              >
                {{ item.name }}, med request by {{ item.practitioner }}
              </li>
            </ul>
          </AccordionContent>
        </AccordionPanel>

        <!-- Allergies -->
        <AccordionPanel value="3">
          <AccordionHeader>Allergies</AccordionHeader>
          <AccordionContent>
            <ul class="text-sm text-gray-800 space-y-1">
              <li v-for="(item, index) in encounterStore.patientProfile?.allergies" :key="index">
                {{ item.code }}, {{ item.displayName }}, {{ item.severity }}
              </li>
            </ul>
          </AccordionContent>
        </AccordionPanel>

        <!-- Immunizations -->
        <AccordionPanel value="4">
          <AccordionHeader>Immunizations</AccordionHeader>
          <AccordionContent>
            <ul class="text-sm text-gray-800 space-y-1">
              <li v-for="(item, index) in encounterStore.patientImmunizations" :key="index">
                {{ item.displayName }}, {{ item.date }}
              </li>
            </ul>
          </AccordionContent>
        </AccordionPanel>
      </Accordion>
    </div>
  </ResizableBox>
</template>
<script setup lang="ts">
import ResizableBox from '@/components/resizable/ResizableBox.vue'
import { computed, ref, watch } from 'vue'
import { useEncounterStore } from '@/stores/encounter.ts'
import Accordion from 'primevue/accordion'
import AccordionPanel from 'primevue/accordionpanel'
import AccordionContent from 'primevue/accordioncontent'
import AccordionHeader from 'primevue/accordionheader'
import { formatDate } from '@/utils/timeMethods'

const encounterStore = useEncounterStore()
const selectedPatientId = ref<string | null>(null)

const vitalSigns = computed(() => encounterStore.vitalSigns)
const lastWeight = computed(() => {
  if (!vitalSigns.value || vitalSigns.value.length === 0) {
    return null
  }

  // Sort entries by date descending
  const sorted = [...vitalSigns.value].sort(
    (a, b) => new Date(b.date!).getTime() - new Date(a.date!).getTime(),
  )

  // Find first BodyWeight
  for (const vital of sorted) {
    const weightMeasurement = vital.measurements?.find((m) => m.type === 'BodyWeight')
    if (weightMeasurement) {
      const kg = parseFloat(weightMeasurement.value!)
      const lbs = kg * 2.20462
      return {
        kg,
        lbs: lbs.toFixed(1), // round to 1 decimal place
      }
    }
  }

  return null
})

const lastHeight = computed(() => {
  if (!vitalSigns.value || vitalSigns.value.length === 0) {
    return null
  }

  // Sort entries by date descending
  const sorted = [...vitalSigns.value].sort(
    (a, b) => new Date(b.date!).getTime() - new Date(a.date!).getTime(),
  )

  // Find first BodyHeight
  for (const vital of sorted) {
    const heightMeasurement = vital.measurements?.find((m) => m.type === 'BodyHeight')
    if (heightMeasurement) {
      const cm = parseFloat(heightMeasurement.value!)
      const totalInches = cm * 0.393701
      const feet = Math.floor(totalInches / 12)
      const inches = Math.round(totalInches % 12)
      return {
        cm,
        feet,
        inches,
      }
    }
  }

  return null
})

const bmi = computed(() => {
  if (!lastWeight.value || !lastHeight.value) {
    return null
  }

  const weightKg = lastWeight.value.kg
  const heightCm = lastHeight.value.cm
  const heightM = heightCm / 100

  if (heightM === 0) {
    return null
  }

  const bmiValue = weightKg / (heightM * heightM)
  return bmiValue.toFixed(1) // rounded to 1 decimal
})

const latestVitalRecord = computed(() => {
  if (!vitalSigns.value || vitalSigns.value.length === 0) {
    return null
  }

  return [...vitalSigns.value].sort(
    (a, b) => new Date(b.date!).getTime() - new Date(a.date!).getTime(),
  )[0]
})

const latestMeasurementsFormatted = computed(() => {
  if (!latestVitalRecord.value) {
    return []
  }

  // Build a map for quick lookup (e.g., BP systolic and diastolic)
  const measurementsMap = Object.fromEntries(
    latestVitalRecord.value.measurements!.map((m) => [m.type, m.value]),
  )

  // Compose formatted labels
  const formatted: string[] = []

  // Blood Pressure
  if (measurementsMap['SystolicBloodPressure'] && measurementsMap['DiastolicBloodPressure']) {
    formatted.push(
      `BP = ${measurementsMap['SystolicBloodPressure']}/${measurementsMap['DiastolicBloodPressure']} mmHg`,
    )
  }

  // Heart Rate
  if (measurementsMap['HeartRate']) {
    formatted.push(`HR = ${measurementsMap['HeartRate']} bpm`)
  }

  // Temperature
  if (measurementsMap['BodyTemperature']) {
    formatted.push(`Temp = ${measurementsMap['BodyTemperature']} °C`)
  }

  // Respiratory Rate
  if (measurementsMap['RespiratoryRate']) {
    formatted.push(`RR = ${measurementsMap['RespiratoryRate']} bpm`)
  }

  // Pain
  if (measurementsMap['PainScale']) {
    formatted.push(`Pain = ${measurementsMap['PainScale']}`)
  }

  // Height
  if (measurementsMap['BodyHeight']) {
    formatted.push(`Height = ${measurementsMap['BodyHeight']} cm`)
  }

  // Height
  if (measurementsMap['BodyWeight']) {
    formatted.push(`Weight = ${measurementsMap['BodyWeight']} kg`)
  }

  return formatted
})

const latestVitalsDate = computed(() => {
  if (!latestVitalRecord.value) {
    return 'Latest Vitals: N/A'
  }

  return `Latest Vitals: ${formatDate(latestVitalRecord.value.date)}`
})

watch(
  () => encounterStore.selectedEncounter,
  (newEncounter) => {
    selectedPatientId.value = newEncounter!.patientId!
  },
)

// const box = computed(
//   () => encounterStore.encounterBoxes.filter((x) => x.type == 'active-patients')[0],
// )

// watch(
//   () => encounterStore.parentContainerWidth,
//   (newValue) => {
//     if (newValue < 1280) {
//       encounterStore.updateEncounterBoxes({
//         ...box.value,
//         isOpen: false,
//       })
//     }
//   },
// )
</script>
