<template>
  <div class="appointments-page">
    <h2 class="font-semibold text-4xl leading-10 text-grey-800 px-4 py-4 sm:px-6 lg:px-8 lg:py-4">
      Appointments
    </h2>
    <div
      :class="`${!isSmallScreen ? 'absolute top-34 right-8 flex gap-2' : 'absolute top-20 right-2 flex gap-2'}`"
    >
      <Select
        v-if="!isSmallScreen"
        v-model="practitionerFilter"
        :options="practitioners"
        optionLabel="text"
        option-value="value"
        placeholder="Practitioner"
        class="w-full md:w-56"
        @change="filter"
      />
      <Button
        icon="pi pi-plus"
        :label="`${!isSmallScreen ? 'Add Appointment' : 'Add'}`"
        @click="toggleMenu"
        class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-primary text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none"
      />
      <Menu ref="menuRef" :model="items" popup />
    </div>

    <div class="px-4 sm:px-6 lg:px-8 mx-auto">
      <div class="flex flex-col">
        <div class="-m-1.5 overflow-x-auto">
          <div class="p-1.5 min-w-full inline-block align-middle">
            <div class="shadow-sm overflow-hidden flex gap-4">
              <!-- FullCalendar (75%) -->
              <div
                :class="`${currentView == 'dayGridMonth' || isSmallScreen ? 'w-full' : 'w-3/4'}`"
              >
                <FullCalendar :options="calendarOptions" ref="calendarRef" />
              </div>

              <!-- Input Field (25%) -->
              <div v-if="currentView != 'dayGridMonth' && !isSmallScreen" class="w-1/4 mt-16">
                <DatePicker
                  v-model="selectedDate"
                  @update:modelValue="onDateChange"
                  inline
                  class="w-full sm:w-[30rem]"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <AddNewPatientAppointment
      :isModalOpen="isNewPatientModalOpen"
      :startDate="selectedStartDate"
      @close="closeModal"
      @switch-form="switchForm"
    />
    <AddExistingPatientAppointment
      :isModalOpen="isExistingPatientModalOpen"
      :startDate="selectedStartDate"
      @close="closeModal"
      @switch-form="switchForm"
    />
    <EditAppointment
      :isModalOpen="isEditAppointmentModalOpen"
      :appointment-id="editAppointmentId"
      @close="closeModal"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, shallowRef, watch, nextTick } from 'vue'
import { api } from '../../api'
import AddNewPatientAppointment from '@/pages/Appointment/AddNewPatientAppointment.vue'
import AddExistingPatientAppointment from '@/pages/Appointment/AddExistingPatientAppointment.vue'

import FullCalendar from '@fullcalendar/vue3'
import dayGridPlugin from '@fullcalendar/daygrid'
import timeGridPlugin from '@fullcalendar/timegrid'
import interactionPlugin, { type DateClickArg } from '@fullcalendar/interaction'
import type {
  CalendarOptions,
  DatesSetArg,
  EventClickArg,
  EventSourceInput,
} from '@fullcalendar/core/index.js'
import listPlugin from '@fullcalendar/list/index.js'
import DatePicker from 'primevue/datepicker'
import Menu from 'primevue/menu'
import Button from 'primevue/button'
import Select from 'primevue/select'
import type { SelectListItem } from '../../api/api-reference'
import EditAppointment from './EditAppointment.vue'
import { useAppointmentsStore } from '@/stores/appointments'

const appointmentsStore = useAppointmentsStore()
const items = ref([
  {
    label: 'New patient',
    command: () => {
      openNewPatientModal(null)
    },
  },
  {
    label: 'Existing patient',
    command: () => {
      openExistingPatientModal()
    },
  },
])

const isNewPatientModalOpen = ref(false)
const isExistingPatientModalOpen = ref(false)
const selectedStartDate = ref<Date | null>(null)

const practitionerFilter = ref('')
const practitioners = ref<SelectListItem[]>([])

const selectedDate = ref(new Date())

const calendarRef = ref<InstanceType<typeof FullCalendar> | null>(null)
const currentView = ref('dayGridMonth')
const isSmallScreen = ref(false)
const events = shallowRef<EventSourceInput | undefined>([])

const startDate = ref<string | null>(null)
const endDate = ref<string | null>(null)

const isEditAppointmentModalOpen = ref(false)
const editAppointmentId = ref('')

const updateVisibleDates = (arg: DatesSetArg) => {
  if (startDate.value != arg.startStr || endDate.value != arg.endStr) {
    startDate.value = arg.startStr // First visible date
    endDate.value = arg.endStr // Last visible date
    fetchData()
  }
}

const updateEventRows = () => {
  const calendarApi = calendarRef.value?.getApi()
  if (!calendarApi) return

  if (window.innerWidth < 1200) {
    isSmallScreen.value = true
    calendarApi.changeView('listWeek')
    calendarApi.setOption('headerToolbar', {
      start: 'prev,next today',
      center: '',
      end: 'title',
    })
  } else if (window.innerWidth < 1600) {
    isSmallScreen.value = false
    calendarApi.changeView('dayGridMonth')
    calendarApi.setOption('dayMaxEventRows', 0)
    calendarApi.setOption('headerToolbar', {
      start: 'prev,next today dayGridMonth,timeGridDay,listDay',
      center: 'title',
      end: '',
    })
  } else {
    isSmallScreen.value = false
    calendarApi.changeView('dayGridMonth')
    calendarApi.setOption('dayMaxEventRows', 2)
    calendarApi.setOption('headerToolbar', {
      start: 'prev,next today dayGridMonth,timeGridDay,listDay',
      center: 'title',
      end: '',
    })
  }
}

const handleEventClick = (info: EventClickArg) => {
  editAppointmentId.value = info.event.id
  isEditAppointmentModalOpen.value = true
  closeFullCalendarPopover()
}
const closeFullCalendarPopover = () => {
  const fcPopover = document.querySelector('.fc-popover')
  if (fcPopover) {
    fcPopover.remove() // Remove popover from DOM
  }
}

const handleSlotClick = async (date: Date) => {
  openNewPatientModal(date)
}

const calendarOptions = ref<CalendarOptions>({
  plugins: [dayGridPlugin, timeGridPlugin, interactionPlugin, listPlugin],
  initialView: currentView.value,
  editable: false,
  selectable: true,
  headerToolbar: {
    start: 'prev,next today dayGridMonth,timeGridDay,listDay',
    center: 'title',
    end: '',
  },
  scrollTime: '09:00:00',
  slotDuration: '00:15:00',
  nowIndicator: true,
  events: events.value,
  dayMaxEventRows: 2,
  allDaySlot: false,
  dateClick: (info: DateClickArg) => {
    if (info.view.type == 'timeGridDay') {
      handleSlotClick(info.date)
    }
    if (calendarRef.value) {
      const calendarApi = calendarRef.value.getApi()
      calendarApi.changeView('timeGridDay', info.dateStr)
    }
  },
  height: '80vh',
  viewDidMount: (info) => {
    currentView.value = info.view.type
  },
  datesSet: (arg: DatesSetArg) => {
    updateVisibleDates(arg)
    nextTick(() => {
      calendarRef.value!.getApi().updateSize() // Force resize on view change
    })
  },
  windowResize: updateEventRows,
  eventClick: handleEventClick,
  eventContent: function (info) {
    // Get the custom properties from extendedProps
    const { patientFullName, employeeShortName, locationName, color } = info.event.extendedProps
    const startTime = info.event.start?.toLocaleTimeString([], {
      hour: 'numeric',
      minute: 'numeric',
    })

    const currentView = info.view.type

    if (currentView === 'dayGridMonth') {
      return {
        html: `
      <div title="${startTime} ${patientFullName} - ${employeeShortName}">
        <span class="time">${startTime} </span><strong>${patientFullName}</strong> - <span style="color: ${color}">${employeeShortName}</span>
      </div>
      `,
      }
    } else if (currentView === 'listWeek' && window.innerWidth < 600) {
      return {
        html: `
      <div>
        Patient: <strong>${patientFullName}</strong><br/><span>Doctor: <strong>${employeeShortName}</strong></span><br/><span>Location: <strong>${locationName}</strong></span>
      </div>
      `,
      }
    } else {
      return {
        html: `
      <div>
        Patient: <strong>${patientFullName}</strong>&emsp;<span>Doctor: <strong>${employeeShortName}</strong></span>&emsp;<span>Location: <strong>${locationName}</strong></span>
      </div>
      `,
      }
    }
  },
})

const onDateChange = () => {
  const calendarApi = calendarRef.value!.getApi()
  const date = selectedDate.value
  const formattedDate = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`
  calendarApi.changeView(currentView.value, formattedDate)
}

watch(currentView, (newView) => {
  if (newView === 'timeGridDay' || newView === 'listDay') {
    console.log('Switched to a detailed view')
  }
  calendarRef.value!.getApi().updateSize()
})

onMounted(async () => {
  updateEventRows()
  await appointmentsStore.getPractitionerLookups()

  practitioners.value = [{ text: 'All Practitioners', value: '' }]
  practitioners.value = [...practitioners.value, ...appointmentsStore.practitionerLookups]
})

watch(events, (newEvents) => {
  calendarOptions.value.events = newEvents // ✅ Ensure updates work correctly
})

const fetchData = async () => {
  try {
    const result = await api.appointments.appointmentListCalendarAppointments({
      employeeId: practitionerFilter.value,
      start: startDate.value,
      end: endDate.value,
    })
    events.value = result.data
  } catch (error) {
    console.error('Error fetching organizations:', error)
  }
}

const openExistingPatientModal = () => {
  isExistingPatientModalOpen.value = true
}

const openNewPatientModal = (date: Date | null) => {
  isNewPatientModalOpen.value = true
  selectedStartDate.value = date
}

const closeModal = () => {
  fetchData()
  isExistingPatientModalOpen.value = false
  isNewPatientModalOpen.value = false
  isEditAppointmentModalOpen.value = false
}

const switchForm = () => {
  isExistingPatientModalOpen.value = !isExistingPatientModalOpen.value
  isNewPatientModalOpen.value = !isNewPatientModalOpen.value
}

const menuRef = ref()
const toggleMenu = (event: MouseEvent) => {
  menuRef.value.toggle(event)
}

const filter = async () => {
  await fetchData()
}
</script>
<style>
.fc-day-today {
  background: white !important;
}

.fc-highlight {
  background: white !important;
}

/* Add a circular border around today's number */
.fc-day-today .fc-daygrid-day-number {
  display: inline-block;
  padding: 2px;
  background-color: #245494; /* Fill the circle with blue */
  color: white; /* Make the number white for contrast */
  border-radius: 50%;
  width: 28px; /* Adjust width for proper circle */
  height: 28px; /* Adjust height for proper circle */
  line-height: center; /* Center text vertically */
  text-align: center; /* Center text horizontally */
}

/* Default Button Styling */
.fc .fc-button {
  @apply py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-primary text-white focus:outline-none focus:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none;
}

/* Button Hover Effect */
.fc .fc-button:hover {
  @apply bg-toroblue-700 border-transparent;
}

.fc-button:focus {
  outline: none !important;
  box-shadow: none !important;
  background-color: #245494 !important;
}

/* Active / Selected Button */
.fc .fc-button-active {
  border-color: transparent !important;
  background-color: #17325a !important;
  box-shadow: none !important;
}

.fc .fc-today-primary {
  box-shadow: none !important;
}

/* Change Today Button */
.fc .fc-today-button {
  @apply bg-primary;
}

.fc-scroller {
  overflow: scroll; /* Enable scrolling */
  scrollbar-width: thin; /* Firefox: Make scrollbar thin */
  scrollbar-color: transparent transparent; /* Firefox: Transparent scrollbar */
}

/* For Webkit browsers (Chrome, Safari) */
.fc-scroller::-webkit-scrollbar {
  width: 6px; /* Set a small width for the scrollbar */
  height: 6px; /* For horizontal scrollbar */
}

.fc-scroller::-webkit-scrollbar-thumb {
  background: transparent; /* Make the thumb transparent */
}

.fc-scroller::-webkit-scrollbar-track {
  background: transparent; /* Make the track transparent */
}

.fc-toolbar-title {
  margin-left: -18rem !important;
  font-size: 1.75rem !important;
}

@media (max-width: 1600px) {
  .fc-toolbar-title {
    margin-left: -12rem !important;
  }
}

@media (max-width: 1200px) {
  .fc-toolbar-title {
    margin-left: 0px !important;
    font-size: 1rem !important;
  }
}

.fc-daygrid-event {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%; /* Ensures it does not exceed the cell */
  display: block;
}

.fc-list-event-time {
  font-size: 1em;
}

@media (max-width: 1024px) {
  .fc-event {
    font-size: 0.7em;
  }
}

/* If you want to hide the scrollbar in mobile views, add this: */
@media (max-width: 720px) {
  .fc-scroller::-webkit-scrollbar {
    display: none;
  }
}

#custom-dropdown {
  font-size: 14px;
  cursor: pointer;
}
</style>
