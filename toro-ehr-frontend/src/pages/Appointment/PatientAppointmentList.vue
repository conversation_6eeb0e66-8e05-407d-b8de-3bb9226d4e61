<template>
  <div class="organization-page">
    <h2 class="font-semibold text-4xl leading-10 text-grey-800 px-4 py-4 sm:px-6 lg:px-8 lg:py-4">
      Appointments
    </h2>
    <TableSection>
      <TableHeader>
        <template #buttons>
          <a
            class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-primary text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none"
            href="#"
            @click="router.push({ name: 'book-appointment' })"
          >
            <PlusIcon class="shrink-0 w-4 h-4"/>
            Request appointment
          </a>
        </template>
      </TableHeader>
      <!-- Table -->
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
        <tr>
          <th scope="col" class="ps-6 py-3 text-start">
            <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Doctor
                </span>
            </div>
          </th>

          <th scope="col" class="ps-6 py-3 text-start">
            <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Location
                </span>
            </div>
          </th>

          <th scope="col" class="px-6 py-3 text-start">
            <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Date
                </span>
            </div>
          </th>

          <th scope="col" class="px-6 py-3 text-start">
            <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Time
                </span>
            </div>
          </th>

          <th scope="col" class="px-6 py-3 text-start">
            <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Duration
                </span>
            </div>
          </th>

          <th scope="col" class="px-6 py-3 text-start">
            <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Status
                </span>
            </div>
          </th>

          <th scope="col" class="px-6 py-3 text-end"></th>
        </tr>
        </thead>

        <tbody class="divide-y divide-gray-200">
        <tr v-for="row in itemList" :key="row.id">
          <td class="h-px w-72 whitespace-nowrap">
            <div class="px-6 py-3">
                <span class="block text-sm font-semibold text-gray-800">{{
                    row.employeeName
                  }}</span>
            </div>
          </td>

          <td class="h-px w-72 whitespace-nowrap">
            <div class="px-6 py-3">
              <span class="block text-sm font-semibold text-gray-500">{{ row.location }}</span>
            </div>
          </td>

          <td class="h-px w-72 whitespace-nowrap">
            <div class="px-6 py-3">
                <span class="block text-sm text-gray-500">{{
                    formatDateTime(row.startAt!).formattedDate
                  }}</span>
            </div>
          </td>

          <td class="h-px w-72 whitespace-nowrap">
            <div class="px-6 py-3">
                <span class="block text-sm text-gray-500">{{
                    formatDateTime(row.startAt!).formattedTime
                  }}</span>
            </div>
          </td>

          <td class="h-px w-72 whitespace-nowrap">
            <div class="px-6 py-3">
              <span class="block text-sm text-gray-500">{{ row.durationInMinutes }} min</span>
            </div>
          </td>

          <td class="h-px w-72 whitespace-nowrap">
            <div class="px-6 py-3">
              <Badge :severity="getStatusSeverity(row.status)">{{ row.status }}</Badge>
            </div>
          </td>

          <td class="h-px w-72 whitespace-nowrap px-6 py-1.5">
            <div class="flex items-right gap-x-4">
              <a v-show="row.status === 'Confirmed'"
                class="inline-flex items-center gap-x-1 text-sm font-medium focus:outline-none"
                :class="{
    'text-primary hover:underline focus:underline cursor-pointer': canCheckIn(row),
    'text-gray-400 cursor-not-allowed': !canCheckIn(row)
  }"
                href="#"
                @click.prevent="canCheckIn(row) && router.push({ name: 'checkin-questionnaires-patient', params: { encounterId: row.encounterId }})"
                v-tooltip="!canCheckIn(row) ? `Available at: ${formatDate(row.checkInAvailableAt!)}` : null"
              >
                Check In
              </a>
              <a
                class="inline-flex items-center gap-x-1 text-sm text-primary font-medium hover:underline focus:outline-none focus:underline"
                href="#"
                @click="
                    router.push({ name: 'book-appointment', query: { appointmentId: row.id } })
                  "
              >
                Reschedule
              </a>
              <a
                class="inline-flex items-center gap-x-1 text-sm text-primary font-medium hover:underline focus:outline-none focus:underline"
                href="#"
                @click="openCancelConfirmation(row)"
              >
                Cancel
              </a>
            </div>
          </td>
        </tr>
        </tbody>
      </table>
      <!-- End Table -->

      <TableFooter
        :totalItems="totalItems"
        :isFirstPage="isFirstPage"
        :isLastPage="isLastPage"
        @prevPage="prevPage"
        @nextPage="nextPage"
      />
    </TableSection>
    <ConfirmDialog
      ref="confirmDialog"
      @confirmedAction="cancelAppointment"
      title="Cancel Appointment"
      :message="cancellationMessage"
    />
  </div>
</template>

<script setup lang="ts">
import {ref, onMounted, computed} from 'vue'
import {api} from '@/api'
import type {AppointmentResponse} from '@/api/api-reference.ts'
import {PlusIcon} from '@heroicons/vue/24/outline'
import TableHeader from '../../components/table/TableHeader.vue'
import TableFooter from '../../components/table/TableFooter.vue'
import TableSection from '../../components/table/TableSection.vue'
import Badge from 'primevue/badge'
import router from '../../router'
import ConfirmDialog from '@/components/form-extensions/ConfirmDialog.vue'
import {formatDate, formatDateTime} from "@/utils/timeMethods.ts";

const limit = 10

const itemList = ref<AppointmentResponse[]>()
const pageNumber = ref(1)
const totalPages = ref(1)
const totalItems = ref(0)
const search = ref('')
const confirmDialog = ref()
const cancellationMessage = ref('Are you sure you want to cancel this appointment?')
const selectedAppointmentId = ref<string>('')

onMounted(async () => {
  await fetchData()
})

const isFirstPage = computed(() => pageNumber.value === 1)
const isLastPage = computed(() => pageNumber.value === totalPages.value)

const nextPage = () => {
  if (!isLastPage.value) {
    pageNumber.value++
    fetchData()
  }
}

const prevPage = () => {
  if (!isFirstPage.value) {
    pageNumber.value--
    fetchData()
  }
}

const fetchData = async () => {
  try {
    const result = await api.appointments.appointmentListPatientAppointments({
      pageNumber: pageNumber.value,
      pageSize: limit,
      searchParam: search.value,
    })
    itemList.value = result.data.items
    pageNumber.value = result.data.pageNumber ?? 1
    totalPages.value = result.data.totalPages ?? 1
    totalItems.value = result.data.totalItems ?? 0
  } catch (error) {
    console.error('Error fetching appointments:', error)
  }
}

const getCancellationWarningMessage = (appointment: AppointmentResponse): string => {
  const { checkInStartOffsetHours, missedAppointmentFeeInCents, startAt } = appointment

  // If no fee or checkin window, show basic message
  if (!missedAppointmentFeeInCents || !checkInStartOffsetHours) {
    return 'Are you sure you want to cancel this appointment?'
  }

  // Calculate if we're within the cancellation fee window
  const appointmentTime = new Date(startAt!)
  const now = new Date()
  const hoursUntilAppointment = (appointmentTime.getTime() - now.getTime()) / (1000 * 60 * 60)

  const feeAmount = (missedAppointmentFeeInCents / 100).toFixed(2)

  if (hoursUntilAppointment <= checkInStartOffsetHours) {
    return `Are you sure you want to cancel this appointment?\n\nA cancellation fee of $${feeAmount} will apply since you are canceling within ${checkInStartOffsetHours} hours of the scheduled time.`
  } else {
    return `Are you sure you want to cancel this appointment?\n\nNote: A cancellation fee of $${feeAmount} may apply if canceled within ${checkInStartOffsetHours} hours of the scheduled time.`
  }
}

const openCancelConfirmation = (appointment: AppointmentResponse) => {
  selectedAppointmentId.value = appointment.id!
  cancellationMessage.value = getCancellationWarningMessage(appointment)
  confirmDialog.value?.open(appointment.id)
}

const cancelAppointment = async (id: string) => {
  try {
    await api.appointments.appointmentCancelAppointment({id: id})
    await fetchData()
  } catch (e) {
    console.log(e)
  }
}

const getStatusSeverity = (status: string | undefined) => {
  if (status)
    switch (status.toLowerCase()) {
      case 'confirmed':
        return 'success' // Green
      case 'pending':
        return 'warning' // Yellow
      case 'canceled':
        return 'danger' // Red
    }
}
const canCheckIn = (row: any) => {
  return new Date() >= new Date(row.checkInAvailableAt)
}
</script>
