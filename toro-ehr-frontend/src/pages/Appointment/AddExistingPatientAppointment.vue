<template>
  <div
    v-if="isModalOpen"
    class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-[99]"
  >
    <div class="bg-white p-6 rounded shadow-md w-full max-w-md max-h-[90vh] overflow-hidden">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-lg font-bold">Existing patient</h2>
        <span
          class="text-primary underline cursor-pointer text-sm flex"
          @click="$emit('switch-form')"
        >
          New Patient <ArrowUpRightIcon class="size-3 m-1"
        /></span>
      </div>
      <div class="overflow-y-auto max-h-[70vh]">
        <form @submit.prevent="createExistingPatientAppointment">
          <div class="relative">
            <div class="my-3">
              <FloatLabel variant="on">
                <AutoComplete
                  v-model="selectedPatient"
                  :suggestions="patientOptions"
                  @complete="searchPatients"
                  @item-select="onPatientSelect"
                  optionLabel="optionText"
                  label="Search patients..."
                  input-id="selectedPatient"
                  :invalid="!!errorMessage"
                  fluid
                />
                <label :for="selectedPatient">Search patients</label>
              </FloatLabel>
            </div>
            <transition name="fade">
              <small v-if="errorMessage" class="error-message">
                {{ errorMessage }}
              </small>
            </transition>
          </div>
          <Select
            id="employeeId"
            label="Practitioner"
            :options="appointmentsStore.practitionerLookups"
          />
          <InputNumber id="durationInMinutes" label="Duration" suffix=" minutes" />
          <DateTimePicker
            id="startAt"
            label="Start"
            showTime
            hourFormat="12"
            dateFormat="m/d/yy"
            :stepMinute="5"
          />
        </form>
        <div class="flex justify-end">
          <button type="button" class="text-gray-500 px-4 py-2 mr-2" @click="$emit('close')">
            Cancel
          </button>
          <button
            @click="createExistingPatientAppointment"
            class="bg-primary text-white px-4 py-2 rounded"
          >
            Save
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useForm, useField } from 'vee-validate'
import { ref, watch } from 'vue'
import { api } from '@/api'
import type {
  CreateExistingPatientAppointmentCommand,
  PatientResponse,
} from '@/api/api-reference.ts'
import * as yup from 'yup'
import debounce from 'lodash.debounce'
import { useAppointmentsStore } from '@/stores/appointments.ts'
import InputNumber from '../../components/form-extensions/InputNumberFluent.vue'
import DateTimePicker from '../../components/form-extensions/DateTimePickerFluent.vue'
import Select from '../../components/form-extensions/SelectFluent.vue'
import AutoComplete, {
  type AutoCompleteCompleteEvent,
  type AutoCompleteOptionSelectEvent,
} from 'primevue/autocomplete'
import FloatLabel from 'primevue/floatlabel'
import { ArrowUpRightIcon } from '@heroicons/vue/24/outline'
import { useAuthStore } from '@/stores/auth'
import { formatDateWithoutTime } from '../../utils/timeMethods'

const authStore = useAuthStore()

interface PatientSelectListitem {
  id: string
  optionText: string
  selectedText: string
}

const appointmentsStore = useAppointmentsStore()

const emit = defineEmits(['close', 'switch-form'])
const props = defineProps<{
  isModalOpen: boolean
  startDate: Date | null
}>()

const patientOptions = ref<PatientSelectListitem[]>([])
const selectedPatient = ref('')

// Handle selection
const onPatientSelect = (event: AutoCompleteOptionSelectEvent) => {
  const patient = event.value as PatientSelectListitem
  selectedPatient.value = patient.selectedText
  setValue(patient.id)
}

const patients = ref<PatientResponse[]>([])

const getInitialValues = () => ({
  patientId: '',
  employeeId: '',
  durationInMinutes: 15,
  startAt: new Date(),
  locationId: authStore.user!.locationId!,
})

const { handleSubmit, resetForm, setFieldValue } = useForm({
  validationSchema: yup.object({
    patientId: yup.string().required('Patient is required'),
    employeeId: yup.string().required('Provider is required'),
    locationId: yup.string().required('Location is required'),
    durationInMinutes: yup.number().required('Duration is required'),
    startAt: yup.string().required('Start at is required'),
  }),
  initialValues: getInitialValues(),
})

const { setValue, errorMessage } = useField<string | null>('patientId')

const createExistingPatientAppointment = handleSubmit(async (values) => {
  try {
    const form: CreateExistingPatientAppointmentCommand = {
      ...values,
      durationInMinutes: Number(values.durationInMinutes),
      startAt: values.startAt.toISOString(),
      initiatedByPatient: false,
    }
    await api.appointments.appointmentCreateForExistingPatient(form)
    resetForm()
    selectedPatient.value = ''
    emit('close')
  } catch (error) {
    console.log(error)
  }
})

const fetchPatients = async (query: string) => {
  if (!query.trim()) {
    patients.value = []
    return
  }

  const patientsResponse = await api.patients.patientListPatients({
    searchParam: query,
  })

  patients.value = patientsResponse.data.items ?? []
  patientOptions.value =
    patientsResponse.data.items?.map((x) => ({
      id: x.id!,
      optionText: `${x.firstName} ${x.lastName} - DOB: ${formatDateWithoutTime(x.birthday)}`,
      selectedText: `${x.firstName} ${x.lastName}`,
    })) ?? []
}

const searchPatients = debounce((event: AutoCompleteCompleteEvent) => {
  fetchPatients(event.query)
}, 300)

watch(
  () => props.isModalOpen,
  async (newValue) => {
    if (newValue) {
      resetForm()
      if (props.startDate) {
        setFieldValue('startAt', props.startDate)
      }
    }
  },
)
</script>
