<template>
  <div class="organization-page">
    <h2 class="font-semibold text-4xl leading-10 text-grey-800 px-4 py-4 sm:px-6 lg:px-8 lg:py-4">
      Payments
    </h2>
    <TableSection>
      <TableHeader>
        <template #inputs>
          <div class="relative max-w-xs">
            <label class="sr-only">Search</label>
            <input
              @input="filter()"
              type="text"
              class="py-2 px-3 ps-9 block w-full border border-gray-200 shadow-sm rounded-lg text-sm focus:z-10 focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none"
              placeholder="Search"
              v-model="search"
            />
            <div class="absolute inset-y-0 start-0 flex items-center pointer-events-none ps-3">
              <i class="pi pi-search text-gray-400"></i>
            </div>
          </div>
        </template>
        <template #buttons>
          <Select
            v-model="filterSelected"
            :options="filterOptions"
            optionLabel="text"
            option-value="value"
            placeholder="Filter"
            class="w-full md:w-56"
            @change="filter"
          />
        </template>
      </TableHeader>
      <!-- Table -->
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
        <tr>
          <th scope="col" class="ps-6 py-3 text-start">
            <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Patient
                </span>
            </div>
          </th>
          <th scope="col" class="ps-6 py-3 text-start">
            <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Encounter Date
                </span>
            </div>
          </th>
          <th scope="col" class="ps-6 py-3 text-start">
            <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Phone
                </span>
            </div>
          </th>
          <th scope="col" class="ps-6 py-3 text-start">
            <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Amount Charged
                </span>
            </div>
          </th>
          <th scope="col" class="ps-6 py-3 text-start">
            <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Amount Refunded
                </span>
            </div>
          </th>
          <th scope="col" class="ps-6 py-3 text-start">
            <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Payment Status
                </span>
            </div>
          </th>
          <th scope="col" class="ps-6 py-3 text-start">
            <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Business Status
                </span>
            </div>
          </th>
          <th scope="col" class="px-6 py-3 text-end"></th>
        </tr>
        </thead>

        <tbody class="divide-y divide-gray-200">
        <tr v-for="row in itemList" :key="row.id">
          <td class="h-px w-72 whitespace-nowrap">
            <div class="px-6 py-3">
                <span class="block text-sm font-semibold text-gray-800">{{
                    row.patientName
                  }}</span>
            </div>
          </td>

          <td class="h-px w-72 whitespace-nowrap">
            <div class="px-6 py-3">
              <span class="block text-sm font-semibold text-gray-500">{{ formatDate(row.encounterDate) }}</span>
            </div>
          </td>

          <td class="h-px w-72 whitespace-nowrap">
            <div class="px-6 py-3">
                <span class="block text-sm text-gray-500">{{
                    row.phoneNumber
                  }}</span>
            </div>
          </td>

          <td class="h-px w-72 whitespace-nowrap">
            <div class="px-6 py-3">
                <span class="block text-sm text-gray-500">${{
                      row.amountCharged?.toFixed(2)
                  }}</span>
            </div>
          </td>

          <td class="h-px w-72 whitespace-nowrap">
            <div class="px-6 py-3">
                <span class="block text-sm text-gray-500">${{
                    row.amountRefunded?.toFixed(2)
                  }}</span>
            </div>
          </td>

          <td class="h-px w-72 whitespace-nowrap">
            <div class="px-6 py-3">
              <span
                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                :class="row.paymentStatus === 'Paid'
                  ? 'bg-green-100 text-green-800'
                  : 'bg-yellow-100 text-yellow-800'"
              >
                <span
                  class="w-1.5 h-1.5 rounded-full mr-1.5"
                  :class="row.paymentStatus === 'Paid' ? 'bg-green-500' : 'bg-yellow-500'"
                ></span>
                {{ row.paymentStatus }}
              </span>
            </div>
          </td>
          <td class="h-px w-72 whitespace-nowrap">
            <div class="px-6 py-3">
              <span
                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                :class="getBusinessStatusClasses(row.businessStatus)"
              >
                <span
                  class="w-1.5 h-1.5 rounded-full mr-1.5"
                  :class="getBusinessStatusDotClass(row.businessStatus)"
                ></span>
                {{ row.businessStatus }}
              </span>
            </div>
          </td>

          <td class="h-px w-72 whitespace-nowrap px-6 py-1.5">
            <div class="flex items-right gap-x-4">
              <a

                class="inline-flex items-center gap-x-1 text-sm text-primary font-medium hover:underline focus:outline-none focus:underline"
                href="#"
                @click.prevent="router.push({ name: 'encounter-payments', params: { encounterId: row.id! } })"
              >
                Payments
              </a>
            </div>
          </td>
        </tr>
        </tbody>
      </table>
      <!-- End Table -->

      <TableFooter
        :totalItems="totalItems"
        :isFirstPage="isFirstPage"
        :isLastPage="isLastPage"
        @prevPage="prevPage"
        @nextPage="nextPage"
      />
    </TableSection>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { api } from '@/api'
import type {EncountersBillingResponse} from '@/api/api-reference.ts'
import TableHeader from '../../components/table/TableHeader.vue'
import TableFooter from '../../components/table/TableFooter.vue'
import TableSection from '../../components/table/TableSection.vue'
import router from '../../router'
import debounce from "lodash.debounce";
import Select from "primevue/select";
import {formatDate} from "@/utils/timeMethods.ts";

const filterOptions = ref([
  { text: "All", value: "All" },
  { text: "Paid", value: "Paid" },
  { text: "Unpaid", value: "Unpaid" }
]);

const filterSelected = ref('All')
const limit = 10
const itemList = ref<EncountersBillingResponse[]>()
const pageNumber = ref(1)
const totalPages = ref(1)
const totalItems = ref(0)
const search = ref('')

onMounted(async () => {
  await fetchData()
})

const isFirstPage = computed(() => pageNumber.value === 1)
const isLastPage = computed(() => pageNumber.value === totalPages.value)

const nextPage = async () => {
  if (!isLastPage.value) {
    pageNumber.value++
    await fetchData()
  }
}

const prevPage = async () => {
  if (!isFirstPage.value) {
    pageNumber.value--
    await fetchData()
  }
}

const fetchData = async () => {
  try {
    const result = await api.encounter.encounterBrowseEncountersBilling({
      pageNumber: pageNumber.value,
      pageSize: limit,
      searchParam: search.value,
      status: filterSelected.value
    })
    itemList.value = result.data.items
    pageNumber.value = result.data.pageNumber ?? 1
    totalPages.value = result.data.totalPages ?? 1
    totalItems.value = result.data.totalItems ?? 0
  } catch (error) {
    console.error('Error fetching payments:', error)
  }
}

const filter = debounce(() => {
  fetchData()
}, 500)

// Business status styling functions
const getBusinessStatusClasses = (status?: string): string => {
  switch (status?.toLowerCase()) {
    case 'planned':
      return 'bg-blue-100 text-blue-800'
    case 'checkedin':
      return 'bg-indigo-100 text-indigo-800'
    case 'arrived':
      return 'bg-purple-100 text-purple-800'
    case 'inprogress':
      return 'bg-yellow-100 text-yellow-800'
    case 'completed':
      return 'bg-green-100 text-green-800'
    case 'missed':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const getBusinessStatusDotClass = (status?: string): string => {
  switch (status?.toLowerCase()) {
    case 'planned':
      return 'bg-blue-400'
    case 'checkedin':
      return 'bg-indigo-400'
    case 'arrived':
      return 'bg-purple-400'
    case 'inprogress':
      return 'bg-yellow-400'
    case 'completed':
      return 'bg-green-400'
    case 'missed':
      return 'bg-red-400'
    default:
      return 'bg-gray-400'
  }
}

</script>
