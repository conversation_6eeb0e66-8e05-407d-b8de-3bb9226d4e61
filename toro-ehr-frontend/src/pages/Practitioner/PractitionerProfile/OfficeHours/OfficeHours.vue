<template>
  <TableSection>
    <TableHeader>
      <template #buttons>
        <a
          class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-primary text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none"
          href="#"
          @click="openEditModal()"
        >
          <PlusIcon class="shrink-0 w-4 h-4" />
          Office Hours
        </a>
        <a
          class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-primary text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none"
          href="#"
          @click="openAddExclusionHoursModal()"
        >
          <PlusIcon class="shrink-0 w-4 h-4" />
          Exlusion Hours
        </a>
      </template>
    </TableHeader>
    <!-- Table -->
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th scope="col" class="ps-6 py-3 text-start">
            <div class="flex items-center gap-x-2">
              <span class="text-xs font-semibold uppercase tracking-wide text-gray-800"> Day </span>
            </div>
          </th>

          <th scope="col" class="px-6 py-3 text-start">
            <div class="flex items-center gap-x-2">
              <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                Open
              </span>
            </div>
          </th>

          <th scope="col" class="px-6 py-3 text-start">
            <div class="flex items-center gap-x-2">
              <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                Close
              </span>
            </div>
          </th>

          <th scope="col" class="px-6 py-3 text-start">
            <div class="flex items-center gap-x-2">
              <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                Exclusion
              </span>
            </div>
          </th>

          <th scope="col" class="px-6 py-3 text-end"></th>
        </tr>
      </thead>

      <tbody class="divide-y divide-gray-200">
        <tr v-for="row in officeHoursList" :key="row.day">
          <td class="h-px w-48 whitespace-nowrap">
            <div class="px-6 py-3">
              <span class="block text-sm font-semibold text-gray-800">{{
                getDayName(row.day!)
              }}</span>
            </div>
          </td>

          <td class="h-px w-48 whitespace-nowrap">
            <div class="px-6 py-3">
              <span class="block text-sm text-gray-500">{{ displayTimeSpan(row.openTime) }}</span>
            </div>
          </td>

          <td class="h-px w-48 whitespace-nowrap">
            <div class="px-6 py-3">
              <span class="block text-sm text-gray-500">{{ displayTimeSpan(row.closeTime) }}</span>
            </div>
          </td>

          <td class="h-px w-72 whitespace-nowrap">
            <div class="px-6 py-3">
              <span v-if="row.openTime && row.closeTime" class="block text-sm text-gray-500">{{
                displayExclusionHours(row.exclusions)
              }}</span>
            </div>
          </td>

          <td class="size-px whitespace-nowrap">
            <Button
              icon="pi pi-pen-to-square"
              aria-label="Edit"
              variant="link"
              v-tooltip="'Edit daily entry'"
              @click="openDailyEditModal(row.day)"
            />
          </td>
        </tr>
      </tbody>
    </table>
    <!-- End Table -->
  </TableSection>

  <Divider />

  <AddOfficeHoursMultipleDays
    :officeHours="officeHoursList"
    :isModalOpen="isModalOpen"
    @close="closeModal"
  />
  <EditOfficeHoursByDay
    :day="selectedDay"
    :officeHours="officeHoursList"
    :isModalOpen="isDailyModalOpen"
    @close="closeModal"
  />
  <AddExclusionHoursMultipleDays
    :officeHours="officeHoursList"
    :isModalOpen="isAddExclusionHoursModalOpen"
    @close="closeModal"
  />
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import type { DayOfWeek, Exclusion, OfficeHours, OutOfOfficeHours } from '@/api/api-reference.ts'
import { useAuthStore } from '@/stores/auth.ts'
import { usePractitionerStore } from '@/stores/practitioner'
import TableSection from '@/components/table/TableSection.vue'
import TableHeader from '@/components/table/TableHeader.vue'
import Divider from 'primevue/divider'
import { getDayName, displayTimeSpan } from '@/utils/timeMethods'
import { PlusIcon } from '@heroicons/vue/24/outline'
import EditOfficeHoursByDay from '../OfficeHours/EditOfficeHoursByDay.vue'
import AddOfficeHoursMultipleDays from './AddOfficeHoursMultipleDays.vue'
import AddExclusionHoursMultipleDays from './AddExclusionHoursMultipleDays.vue'
import Button from 'primevue/button'

const authStore = useAuthStore()
const practitionerStore = usePractitionerStore()

const officeHoursList = ref<OfficeHours[]>()
const outOfOfficeHoursList = ref<OutOfOfficeHours[]>()

const isModalOpen = ref(false)
const isAddExclusionHoursModalOpen = ref(false)
const isDailyModalOpen = ref(false)
const selectedDay = ref<DayOfWeek | undefined>()

onMounted(async () => {
  officeHoursList.value = practitionerStore.organizationPractitioner?.officeHours
  outOfOfficeHoursList.value = practitionerStore.organizationPractitioner?.outOfOfficeHours
})

watch(
  () => practitionerStore.organizationPractitioner,
  (newValue) => {
    officeHoursList.value = newValue?.officeHours
    outOfOfficeHoursList.value = newValue?.outOfOfficeHours
  },
)

const openEditModal = () => {
  isModalOpen.value = true
}

const openAddExclusionHoursModal = () => {
  isAddExclusionHoursModalOpen.value = true
}

const openDailyEditModal = (day: DayOfWeek | undefined) => {
  selectedDay.value = day
  isDailyModalOpen.value = true
}

const closeModal = () => {
  practitionerStore.getLocationEmployee(authStore.user!.employeeId!, authStore.user!.locationId!)
  isModalOpen.value = false
  isDailyModalOpen.value = false
  isAddExclusionHoursModalOpen.value = false
}

const displayExclusionHours = (exclusionHours: Exclusion[] | undefined) => {
  return (
    exclusionHours
      ?.map(
        (e) =>
          `${displayTimeSpan(e.from)} - ${displayTimeSpan(e.to)}${e.title ? ` (${e.title})` : ''}`,
      )
      .join(', ') ?? ''
  )
}
</script>
