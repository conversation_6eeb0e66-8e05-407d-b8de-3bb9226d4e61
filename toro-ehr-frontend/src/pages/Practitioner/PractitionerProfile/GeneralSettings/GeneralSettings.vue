<template>
  <form @submit.prevent="setGeneralSettings">
    <p class="text-sm font-light text-gray-700">
      How many appointments would like to allow to overlap? 0 means a patient can book an
      appointment that does not overlap any other appointments. 1 overlap means up to 2 patients can
      book the same time slot. Note that a provider can add an overlapping appointment, this affects
      only what patients can book.
    </p>
    <Select
      id="numberOfAppointmentOverlaps"
      label="Number of Overlaps"
      :options="numberOfAppointmentsOptions"
    />
    <p class="text-sm font-light text-gray-700">
      How long would you like the appointment slots to be when a patient is booking?
    </p>
    <InputNumber id="appointmentDurationInMinutes" label="Appointment Duration" suffix=" minutes" />
    <p class="text-sm font-light text-gray-700">
      Do you want to be asked to confirm every new patient appointment or allow patients to confirm
      appointments according to your provided schedule?
    </p>
    <div class="flex flex-wrap gap-1">
      <label for="pendingApprovalAppointments">Require Manual Confirmation</label>
      <Checkbox id="pendingApprovalAppointments" v-model="pendingApprovalAppointments" binary />
    </div>
    <ColorPicker id="calendarColor" label="Calendar color" wrapper-class="mt-4" />
    <div class="relative mt-6">
      <label class="absolute px-1 bg-white text-sm font-medium text-gray-700 left-2 -top-2.5">
        Receive notification for:
      </label>
      <div class="border border-gray-300 rounded-lg p-2 mt-3">
        <div class="flex flex-wrap gap-4">
          <div class="flex items-center gap-2 p-2">
            <Checkbox
              v-model="receivedNotificationPreferences"
              inputId="Booked"
              name="receivedNotificationPreferences"
              value="Booked"
            />
            <label for="Booked">Booked Appointments</label>
          </div>
          <div class="flex items-center gap-2">
            <Checkbox
              v-model="receivedNotificationPreferences"
              inputId="Rescheduled"
              name="receivedNotificationPreferences"
              value="Rescheduled"
            />
            <label for="Rescheduled">Rescheduled Appointments</label>
          </div>
          <div class="flex items-center gap-2">
            <Checkbox
              v-model="receivedNotificationPreferences"
              inputId="Canceled"
              name="receivedNotificationPreferences"
              value="Canceled"
            />
            <label for="Canceled">Canceled Appointments</label>
          </div>
        </div>
      </div>
    </div>

    <Divider />
    <div class="card flex justify-end mb-4">
      <Button label="Save" type="submit" />
    </div>
  </form>
</template>

<script setup lang="ts">
import { watch } from 'vue'
import Select from '@/components/form-extensions/SelectFluent.vue'
import InputNumber from '@/components/form-extensions/InputNumberFluent.vue'
import Divider from 'primevue/divider'
import Button from 'primevue/button'
import { api } from '@/api'
import { useField, useForm } from 'vee-validate'
import * as yup from 'yup'
import { useAuthStore } from '@/stores/auth.ts'
import { usePractitionerStore } from '@/stores/practitioner'
import ColorPicker from '@/components/form-extensions/ColorPickerFluent.vue'
import Checkbox from 'primevue/checkbox'
import { useToast } from 'vue-toastification'
import type { EditGeneralSettingsRequest } from '../../../../api/api-reference'

const toast = useToast()
const authStore = useAuthStore()
const practitionerStore = usePractitionerStore()
const numberOfAppointmentsOptions = [
  {
    text: '0 appointment',
    value: 0,
  },
  {
    text: '1 appointment',
    value: 1,
  },
  {
    text: '2 appointments',
    value: 2,
  },
  {
    text: '3 appointments',
    value: 3,
  },
  {
    text: '4 appointments',
    value: 4,
  },
  {
    text: '5 appointments',
    value: 5,
  },
]

watch(
  () => practitionerStore.organizationPractitioner,
  (newValue) => {
    const generalSettings = {
      numberOfAppointmentOverlaps: newValue?.numberOfAppointmentOverlaps,
      appointmentDurationInMinutes: newValue?.appointmentDurationInMinutes,
      calendarColor: newValue?.calendarColor,
      receivedNotificationPreferences: newValue?.receivedNotificationPreferences,
    }
    setValues(generalSettings)
  },
)

const { handleSubmit, setValues } = useForm({
  validationSchema: yup.object({
    numberOfAppointmentOverlaps: yup.string().required('Number of overlaps is required'),
    pendingApprovalAppointments: yup
      .boolean()
      .required('Pending Approval Appointments is required'),
    appointmentDurationInMinutes: yup.number().required('Appointment Duration is required'),
    calendarColor: yup.string().required('Calendar Color is required'),
    receivedNotificationPreferences: yup.array().nullable(),
  }),
  initialValues: practitionerStore.organizationPractitioner,
})

const { value: receivedNotificationPreferences } = useField<string[]>(
  'receivedNotificationPreferences',
)
const { value: pendingApprovalAppointments } = useField<string[]>('pendingApprovalAppointments')

const setGeneralSettings = handleSubmit(async (values) => {
  try {
    const employeeId = authStore.user!.employeeId!
    const locationId = authStore.user!.locationId!

    const form: EditGeneralSettingsRequest = {
      ...values,
      pendingApprovalAppointments: values.pendingApprovalAppointments ?? false,
    }
    await api.employees.employeeEditLocationEmployeeGeneralSettings(
      authStore.user!.employeeId!,
      authStore.user!.locationId!,
      form,
    )
    toast.success('Settings have been successfully updated!')
    practitionerStore.getLocationEmployee(employeeId, locationId)
  } catch (error) {
    console.log(error)
  }
})
</script>
