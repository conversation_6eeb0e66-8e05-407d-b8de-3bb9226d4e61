<template>
  <div class="flex min-h-full flex-col px-6 py-36 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-sm">
      <div class="flex items-center w-full leading-9">
        <img src="../../assets/logo_black.png" alt="logo" />
      </div>
      <h2 class="mt-6 text-center text-2xl font-bold leading-9 tracking-tight text-gray-900">
        Set password
      </h2>
    </div>

    <div class="mt-10 sm:mx-auto sm:w-full sm:max-w-sm">
      <form @submit.prevent="setPassword">
        <InputText id="password" label="Password" type="password" v-model="formData.password" />
        <InputText
          id="confirmPassword"
          label="Confirm password"
          type="password"
          v-model="formData.confirmPassword"
        />
        <div class="mt-5">
          <button
            type="submit"
            class="flex w-full justify-center rounded-md bg-toroblue-500 px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-toroblue-400 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-toroblue-600"
          >
            Set password
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import * as yup from 'yup'
import { onMounted, reactive } from 'vue'
import InputText from '../../components/form-extensions/InputText.vue'
import { useForm } from 'vee-validate'
import { useAuthStore } from '@/stores/auth'
import { useRoute } from 'vue-router'

const route = useRoute()

const formData = reactive({
  password: '',
  confirmPassword: '',
  invitationToken: '',
})

useForm({
  validationSchema: yup.object({
    password: yup.string().required(),
    confirmPassword: yup
      .string()
      .oneOf([yup.ref('password'), undefined], 'Password must match')
      .required('Confirm password is requird'),
    invitationToken: yup.string().required(),
  }),
})

const getQueryParam = (key: string): string | null => {
  const params = new URLSearchParams(window.location.search)
  return params.get(key)
}

onMounted(() => {
  if (authStore.user) {
    authStore.removeUserAndLocalStorage()
  }
  const invitationToken = getQueryParam('invitationToken')
  if (invitationToken) {
    formData.invitationToken = invitationToken
  }
})

const authStore = useAuthStore()

const setPassword = async () => {
  try {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { confirmPassword, ...setPasswordCommand } = formData
    await authStore.setPassword(setPasswordCommand, route.name == 'set-password-patient')
  } catch (error) {
    console.log(error)
  }
}
</script>
